<template>
  <div class="app-container" v-loading="isLoading">
    <div class="w-map" id="airline-map"></div>

    <div class="w-right">
      <div class="w-line-name">
        <span>{{ fromData.routesInfo.routesName }}</span>
        <i @click="editLineName" class="el-icon-edit"></i>
      </div>

      <div class="w-info">
        <div>
          <p>{{ this.fromData.routesPointList.length }}</p>
          <p>航点数</p>
        </div>
        <div>
          <p>{{ routesLength }}Km</p>
          <p>预计飞行里程</p>
        </div>
        <div>
          <p>
            {{
              (
                (routesLength * 1000) /
                fromData.routesConfig.autoFlightSpeed /
                60
              ).toFixed(1)
            }}min
          </p>
          <p>预计飞行时间</p>
        </div>
      </div>

      <div class="w-switch">
        <div :class="{ 'w-active': isDropLine }" @click="isDropLine = true">
          航线设置
        </div>
        <div :class="{ 'w-active': !isDropLine }" @click="switchDropLine">
          航点设置
        </div>
      </div>

      <div class="w-line-info" v-if="isDropLine">
        <div class="w-cell">
          <div class="w-title">
            <span>飞行速度</span>
            <span>{{ fromData.routesConfig.autoFlightSpeed }}m/s</span>
          </div>
          <div class="w-content">
            <div class="w-content-img">
              <img
                @click="fromData.routesConfig.autoFlightSpeed--"
                src="@/assets/images/jian.png"
                alt=""
              />
            </div>
            <div class="w-content-slider">
              <el-slider
                :min="5"
                :max="15"
                v-model="fromData.routesConfig.autoFlightSpeed"
              ></el-slider>
            </div>
            <div class="w-content-img">
              <img
                @click="fromData.routesConfig.autoFlightSpeed++"
                src="@/assets/images/jia.png"
                alt=""
              />
            </div>
          </div>
        </div>

        <div class="w-cell">
          <div class="w-title">
            <span>无人机偏航角</span>
            <span></span>
          </div>
          <div class="w-content">
            <w-select
              :value.sync="fromData.routesConfig.waypointHeadingMode"
              :option="waypointHeadingModeOption"
            />
          </div>
        </div>

        <div class="w-cell">
          <div class="w-title">
            <span>完成动作</span>
            <span></span>
          </div>
          <div class="w-content">
            <w-select
              :value.sync="fromData.routesConfig.finishAction"
              :option="finishActionOption"
            />
          </div>
        </div>

        <div class="w-cell">
          <div class="w-title">
            <span>机型</span>
            <span></span>
          </div>
          <div class="w-content">
            <w-select
              :value.sync="fromData.routesConfig.droneSubEnumValue"
              :option="droneSubEnumValueOption"
            />
          </div>
        </div>
      </div>

      <div class="w-line-info" v-else>
        <div class="w-carousel">
          <i class="el-icon-caret-left" @click="switchPoint('-')"></i>
          <span>航点{{ routesPointObj.index + 1 }}</span>
          <i class="el-icon-caret-right" @click="switchPoint('+')"></i>
        </div>
        <div class="w-line-info-box">
          <div class="w-cell">
            <div class="w-title">
              <span>飞行高度</span>
              <span>{{ routesPointObj.height }}m</span>
            </div>
            <div class="w-content">
              <div class="w-content-img">
                <img
                  @click="routesPointObj.height--"
                  src="@/assets/images/jian.png"
                  alt=""
                />
              </div>
              <div class="w-content-slider">
                <el-slider
                  @change="pointHeightChange"
                  :min="20"
                  :max="500"
                  v-model="routesPointObj.height"
                ></el-slider>
              </div>
              <div class="w-content-img">
                <img
                  @click="routesPointObj.height++"
                  src="@/assets/images/jia.png"
                  alt=""
                />
              </div>
            </div>
          </div>

          <div class="w-cell">
            <div class="w-title">
              <span>云台俯仰角</span>
              <span>{{ routesPointObj.gimbalPitchAngle }}°</span>
            </div>
            <div class="w-content">
              <div class="w-content-img">
                <img
                  @click="routesPointObj.gimbalPitchAngle--"
                  src="@/assets/images/jian.png"
                  alt=""
                />
              </div>
              <div class="w-content-slider">
                <el-slider
                  :min="-90"
                  :max="30"
                  v-model="routesPointObj.gimbalPitchAngle"
                ></el-slider>
              </div>
              <div class="w-content-img">
                <img
                  @click="routesPointObj.gimbalPitchAngle++"
                  src="@/assets/images/jia.png"
                  alt=""
                />
              </div>
            </div>
          </div>

          <div class="w-cell">
            <div class="w-title">
              <span>无人机偏航角</span>
              <span></span>
            </div>
            <div class="w-content">
              <w-select
                :value.sync="routesPointObj.waypointHeadingMode"
                :option="waypointHeadingModeOption"
              />
            </div>
          </div>

          <div class="w-cell">
            <div class="w-title">
              <span>添加动作</span>
              <span></span>
            </div>
            <div class="w-content">
              <w-select
                :isselect="true"
                @change="selectAction"
                title="添加动作"
                :option="routesPointActionListOption"
              />
            </div>
          </div>

          <div class="w-action">
            <draggable
              v-model="routesPointObj.routesPointActionList"
              :options="dragOptions"
              @update="actionDraggable"
              handle=".el-icon-rank"
            >
              <transition-group tag="div" id="doing" class="item-d">
                <div
                  class="w-cell"
                  v-for="item in routesPointObj.routesPointActionList"
                  :key="item.index"
                >
                  <div class="w-title">
                    <div class="w-title-left">
                      <i class="el-icon-rank"></i>
                      <span>{{
                        routesPointActionListOption.find(
                          (item1) => item1.value == item.actionActuatorFunc
                        ).label
                      }}</span>
                    </div>
                    <div class="w-title-right">
                      <span v-if="item.actionActuatorFunc == 'zoom'"
                        >{{ item.value }}倍</span
                      >
                      <span v-if="item.actionActuatorFunc == 'rotateYaw'"
                        >{{ item.value }}°</span
                      >
                      <span v-if="item.actionActuatorFunc == 'hover'"
                        >{{ item.value }}s</span
                      >
                      <span v-if="item.actionActuatorFunc == 'multipleTiming'"
                        >{{ item.value }}s</span
                      >
                      <span v-if="item.actionActuatorFunc == 'multipleDistance'"
                        >{{ item.value }}m</span
                      >
                      <i class="el-icon-delete" @click="deleteAction(item)"></i>
                    </div>
                  </div>
                  <div
                    class="w-content"
                    v-if="
                      item.actionActuatorFunc == 'zoom' ||
                      item.actionActuatorFunc == 'rotateYaw' ||
                      item.actionActuatorFunc == 'hover' ||
                      item.actionActuatorFunc == 'multipleTiming' ||
                      item.actionActuatorFunc == 'multipleDistance'
                    "
                  >
                    <div class="w-content-img">
                      <img
                        @click="item.value--"
                        src="@/assets/images/jian.png"
                        alt=""
                      />
                    </div>
                    <div class="w-content-slider">
                      <el-slider
                        :min="item.actionActuatorFunc == 'rotateYaw' ? -180 : 1"
                        :max="actionMax(item.actionActuatorFunc)"
                        v-model="item.value"
                      ></el-slider>
                    </div>
                    <div class="w-content-img">
                      <img
                        @click="item.value++"
                        src="@/assets/images/jia.png"
                        alt=""
                      />
                    </div>
                  </div>
                </div>
              </transition-group>
            </draggable>
          </div>
        </div>
      </div>

      <div class="w-btn">
        <el-button type="primary" @click="addRouteLine">保存设置</el-button>
      </div>
    </div>

    <div class="w-right-tool">
      <el-tooltip class="item" effect="dark" content="位置搜索" placement="top">
        <img
          @click="ShowAddressSearch"
          src="@/assets/images/search.png"
          alt=""
        />
      </el-tooltip>
      <!-- <el-tooltip
        class="item"
        effect="dark"
        content="地图类型切换"
        placement="top"
      >
        <img @click="switchLayer" src="@/assets/images/qiehaun.png" alt="" />
      </el-tooltip> -->
      <el-tooltip class="item" effect="dark" content="地图放大" placement="top">
        <img @click="addZoom" src="@/assets/images/fangda.png" alt="" />
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="地图缩小" placement="top">
        <img @click="reduceZoom" src="@/assets/images/suoxiao.png" alt="" />
      </el-tooltip>
    </div>
    <div class="w-right-search" v-if="isShowAddressSearch">
      <el-select
        style="width: 100%; height: 40px"
        v-model="addressSearch"
        filterable
        remote
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="remoteMethod"
        @change="addressSelect"
        :loading="addressSearchLoading"
      >
        <el-option
          v-for="item in addressOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
    </div>
  </div>
</template>

<script>
import {
  routesList,
  DeleteRoutes,
  addRoutes,
  routesDet,
  putRoutes,
} from "@/api/cruise/airline";
import { uploadPlanImg } from "@/api/command/plan";
import AMapLoader from "@amap/amap-jsapi-loader";
import { amapKey, amapSecretkey } from "@/utils/use-g-map";
import html2canvas from "html2canvas";
import { createUniqueString } from "@/utils/index";
import { detAirport } from "@/api/cruise/airdrome";
import wSelect from "@/views/components/lineselect";
import draggable from "vuedraggable";
export default {
  name: "Dict",
  components: {
    wSelect,
    draggable,
  },
  data() {
    return {
      map: null, // 地图实例
      zoom: 10,
      geocoder: null, // 经纬度转地址插件对象
      placeSearch: null, // 存放地图搜索对象
      isShowAddressSearch: false, //是否显示搜索
      addressSearch: "", // 地址选择
      addressOptions: [], // 地址搜索列表
      addressSearchLoading: false, // 地址搜索加载
      addressMarker: null, // 搜索到地址的点位对象
      isaddLayer: false,
      satellite: null, //卫星图层
      sector: {
        // 机场安全区域信息
        areaNum: 8, // 区域数量
        tilt: 10, // 倾斜角
        height: 150, // 默认高度
        areaList: [
          { areaName: "A区", radius: 3000 },
          { areaName: "B区", radius: 4000 },
          { areaName: "C区", radius: 5000 },
          { areaName: "D区", radius: 2000 },
          { areaName: "E区", radius: 1000 },
          { areaName: "F区", radius: 3500 },
          { areaName: "G区", radius: 3000 },
          { areaName: "H区", radius: 4000 },
        ],
        constructAreaList: [], // 超高建筑点位
      },
      fromData: {
        routesInfo: {
          airportId: null,
          routesName: "",
          routesStartPoint: "",
          routesEndPoint: "",
          routesUrl: "",
          routesId: "",
          routeMileage: 0,
        },
        routesConfig: {
          flyToWaylineMode: "safely",
          finishAction: "goHome",
          exitOnRCLost: "goContinue",
          executeRCLostAction: "goBack",
          takeOffSecurityHeight: 20,
          globalTransitionalSpeed: 10,
          droneEnumValue: 67,
          droneSubEnumValue: 0,
          autoFlightSpeed: 10,
          globalHeight: 300,
          gimbalPitchMode: "usePointSetting",
          waypointHeadingMode: "followWayline",
          waypointHeadingPathMode: "followBadArc",
          globalWaypointTurnMode: "toPointAndStopWithDiscontinuityCurvature",
        },
        routesPointList: [],
      },
      routesPointObj: {
        coordinates: "", //点位经纬度
        index: 0,
        useGlobalHeight: 0,
        ellipsoidHeight: 88,
        height: 99,
        useGlobalSpeed: 1,
        waypointSpeed: 10,
        gimbalPitchAngle: -30,
        executeHeight: 88,
        useGlobalHeadingParam: 1,
        waypointHeadingMode: "followWayline",
        waypointHeadingPathMode: "followBadArc",
        routesPointActionList: [
          // {
          //     index: 0,
          //     actionActuatorFunc: "takePhoto",
          //     value: "",
          //     activeName: ""
          // }
        ],
      },
      waypointHeadingModeOption: [
        //无人机偏航角
        { label: "沿航线方向", value: "followWayline" },
        { label: "手动控制", value: "manually" },
        { label: "锁定当前偏航角", value: "fixed" },
      ],
      finishActionOption: [
        { label: "自动返航", value: "goHome" },
        { label: "原地降落", value: "autoLand" },
      ],
      droneSubEnumValueOption: [
        { label: "M40", value: 0 },
        { label: "M40T", value: 1 },
      ],
      routesPointActionListOption: [
        { label: "拍照", value: "takePhoto" },
        { label: "开始录像", value: "startRecord" },
        { label: "结束录像", value: "stopRecord" },
        { label: "相机变焦", value: "zoom" },
        { label: "飞行器偏航", value: "rotateYaw" },
        { label: "悬停等待", value: "hover" },
        { label: "开始等时间隔拍照", value: "multipleTiming" },
        { label: "开始等距间隔拍照", value: "multipleDistance" },
        { label: "结束间隔拍照", value: "multipleStop" },
      ],
      isDropLine: true,
      airport: {}, //机场信息
      superelevationList: [],
      mouseText: null,
      mouseTimeOut: null,
      markerList: [], // 航点对象
      icon1: null, // 绿
      icon2: null, //蓝
      icon3: null, //黄
      timestamp: null, // 节流时间戳，公用
      contextMenu: null, // 航点删除菜单
      lineCanvas: null, // 航线canvas对象
      lineCustomLayer: null, // 航线图层对象
      dragOptions: {
        animation: 120,
        scroll: true,
        group: "sortlist",
        ghostClass: "ghost-style",
      },
      addPointBtnObj: null, // 航线上的加号图标
      routesLength: 0, // 航线总长度
      isScreenshot: false, // 是否截图完成
      saveTimer: null,
      isAddress1: false,
      isAddress2: false,
      isLoading: false,
    };
  },
  created() {},
  mounted() {
    if (this.$route.params.id == 0) {
      this.fromData.routesInfo.airportId = this.$route.query.airportId;
      this.fromData.routesInfo.routesName = this.$route.query.routesName;
      this.initMap();
    } else {
      console.log("this.$route.params", this.$route.params);
      this.getRoutesDet();
    }
  },
  methods: {
    getRoutesDet() {
      routesDet(this.$route.params.id).then((res) => {
        console.log("航线信息", res);
        this.fromData = res.data;
        this.initMap();
      });
    },
    // 截图高德地图
    getMapImg() {
      let _this = this;
      window.pageYOffset = 0;
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
      html2canvas(document.getElementById("airline-map"), {
        backgroundColor: null, //画出来的图片有白色的边框,不要可设置背景为透明色（null）
        useCORS: true, //支持图片跨域
        scale: 0.5, //设置放大的倍数
      }).then((canvas) => {
        // console.log("canvas",canvas)
        canvas.toBlob((blob) => {
          let fd = new FormData();
          fd.append("file", blob);
          uploadPlanImg(fd, _this.uploadProgress).then((res) => {
            // console.log("res",res)
            _this.fromData.routesInfo.routesUrl = res.url;
            _this.isScreenshot = true;
          });
        });
      });
    },
    uploadProgress(e) {
      console.log("进度", e);
    },
    addRouteLine() {
      let _this = this;
      console.log("this.fromData", this.fromData);
      if (this.fromData.routesPointList.length < 2) {
        this.$modal.msgWarning("最少添加两个航点！");
        return;
      }
      // console.log(this.geocoder)
      // return;
      this.isLoading = true;
      this.getMapImg();

      this.geocoder.getAddress(
        this.fromData.routesPointList[0].coordinates.split(","),
        (status, result) => {
          console.log("status", status);
          console.log("result", result);
          if (status == "complete" && result.regeocode) {
            _this.fromData.routesInfo.routesStartPoint =
              result.regeocode.formattedAddress;
            _this.isAddress1 = true;
          }
        }
      );
      this.geocoder.getAddress(
        this.fromData.routesPointList[
          this.fromData.routesPointList.length - 1
        ].coordinates.split(","),
        (status, result) => {
          console.log("status", status);
          console.log("result", result);
          if (status == "complete" && result.regeocode) {
            _this.fromData.routesInfo.routesEndPoint =
              result.regeocode.formattedAddress;
            _this.isAddress2 = true;
          }
        }
      );

      _this.saveTimer = setInterval(() => {
        if (_this.isScreenshot && _this.isAddress1 && _this.isAddress2) {
          clearInterval(_this.saveTimer);
          _this.saveTimer = null;
          if (this.fromData.routesInfo.routesId == "") {
            addRoutes(this.fromData)
              .then((res) => {
                console.log("航线保存", res);
                this.$modal.msgSuccess("保存成功");
                _this.isLoading = false;
                _this.$router.go(-1);
              })
              .catch((err) => {
                console.log(err);
                _this.isLoading = false;
              });
          } else {
            putRoutes(this.fromData)
              .then((res) => {
                console.log("航线保存", res);
                this.$modal.msgSuccess("保存成功");
                _this.isLoading = false;
                _this.$router.go(-1);
              })
              .catch((err) => {
                console.log(err);
                _this.isLoading = false;
              });
          }
        }
      }, 1000);
    },
    deleteAction(data) {
      this.routesPointObj.routesPointActionList.splice(data.index, 1);
      this.routesPointObj.routesPointActionList.forEach((item, index) => {
        item.index = index;
      });
    },
    actionDraggable(e) {
      this.routesPointObj.routesPointActionList.forEach((item, index) => {
        item.index = index;
      });
    },
    actionMax(actionActuatorFunc) {
      switch (actionActuatorFunc) {
        case "zoom":
          return 200;
        case "rotateYaw":
          return 180;
        case "hover":
          return 30;
        case "multipleTiming":
          return 30;
        case "multipleDistance":
          return 100;
        default:
          break;
      }
    },
    pointHeightChange(val) {
      console.log(val);
      this.drawAllMarker();
    },
    // 航线航点切换
    switchDropLine() {
      if (this.fromData.routesPointList.length != 0) {
        if (this.routesPointObj.coordinates == "") {
          this.routesPointObj = this.fromData.routesPointList[0];
          this.markerList.forEach((item, index) => {
            if (index == this.routesPointObj.index) {
              item.marker.setIcon(this.icon2);
            } else {
              if (item.isisPointInRing) {
                item.marker.setIcon(this.icon3);
              } else {
                item.marker.setIcon(this.icon1);
              }
            }
          });
        }
        console.log("this.routesPointObj", this.routesPointObj);
        this.isDropLine = false;
      } else {
        this.$modal.msg("请添加航点！");
      }
    },
    // 切换航点
    switchPoint(character) {
      if (character == "-") {
        if (this.routesPointObj.index > 0) {
          this.routesPointObj =
            this.fromData.routesPointList[this.routesPointObj.index - 1];
        }
      } else {
        if (
          this.routesPointObj.index <
          this.fromData.routesPointList.length - 1
        ) {
          this.routesPointObj =
            this.fromData.routesPointList[this.routesPointObj.index + 1];
        }
      }
      this.markerList.forEach((item, index) => {
        if (index == this.routesPointObj.index) {
          item.marker.setIcon(this.icon2);
        } else {
          if (item.isisPointInRing) {
            item.marker.setIcon(this.icon3);
          } else {
            item.marker.setIcon(this.icon1);
          }
        }
      });
    },
    // 添加动作
    selectAction(data) {
      console.log(data);
      let obj = {
        index: this.routesPointObj.routesPointActionList.length,
        actionActuatorFunc: data,
        value: "",
      };
      switch (data) {
        case "takePhoto":
          break;
        case "startRecord":
          break;
        case "stopRecord":
          break;
        case "zoom":
          obj.value = 2;
          break;
        case "rotateYaw":
          obj.value = 0;
          break;
        case "hover":
          obj.value = 15;
          break;
        case "multipleTiming":
          obj.value = 15;
          break;
        case "multipleDistance":
          obj.value = 50;
          break;
        case "multipleStop":
          break;
      }
      this.routesPointObj.routesPointActionList.push(obj);
    },
    //修改航线名称
    editLineName() {
      this.$prompt("请输入目标点名称", "重命名", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputValue: this.fromData.routesInfo.routesName,
      })
        .then(({ value }) => {
          this.fromData.routesInfo.routesName = value;
        })
        .catch(() => {});
    },
    // 获取机场安全区域
    getDetAirport() {
      let _this = this;
      let _id = this.fromData.routesInfo.airportId
        ? this.fromData.routesInfo.airportId
        : this.fromData.routesInfo.routesId;
      detAirport(_id).then((res) => {
        console.log("机场信息", res);
        this.airport = res.data;
        this.zoom = 14;
        this.map.setZoomAndCenter(_this.zoom, [
          res.data.longitude,
          res.data.latitude,
        ]);
        if (res.data.sector) {
          this.sector = JSON.parse(res.data.sector);
          this.airport.sector = this.sector;
          console.log("安全区域信息", this.sector);
          this.loadAllGon();
          this.drawFan(
            res.data.longitude,
            res.data.latitude,
            -90 + Number(this.sector.tilt),
            360 / this.sector.areaNum,
            this.sector.areaList
          );
          this.fromData.routesConfig.globalHeight = Number(this.sector.height);
        }
        var icon = new AMap.Icon({
          size: new AMap.Size(30, 30),
          image: require("@/assets/images/hezi.png"),
          imageSize: new AMap.Size(30, 30),
          imageOffset: new AMap.Pixel(0, 0),
        });
        let labelContent = `<div class='labelContent'>${res.data.airportName}</div>`;
        let marker = new AMap.Marker({
          position: [res.data.longitude, res.data.latitude],
          icon: icon,
          offset: new AMap.Pixel(-15, -15),
          draggable: false,
          label: {
            direction: "bottom",
            content: labelContent,
            offset: new AMap.Pixel(0, 5),
          },
        });
        _this.map.add(marker);
        _this.drawAllMarker();
      });
    },
    // 初始化地图
    initMap() {
      let _this = this;
      window._AMapSecurityConfig = {
        securityJsCode: amapSecretkey,
      };
      AMapLoader.load({
        key: amapKey,
        version: "2.0",
        plugins: ["AMap.Geocoder", "AMap.PlaceSearch"],
      })
        .then((AMap) => {
          _this.satellite = new AMap.TileLayer.Satellite();
          _this.isaddLayer = true;
          _this.map = new AMap.Map("airline-map", {
            zoom: _this.zoom,
            defaultCursor: "pointer",
            WebGLParams: { preserveDrawingBuffer: true },
            layers: [new AMap.TileLayer.RoadNet({ opacity: 0.6 })],
            // mapStyle: "amap://styles/whitesmoke",
          });
          _this.map.addLayer(_this.satellite);
          _this.geocoder = new AMap.Geocoder({
            radius: 1000, //范围，默认：500
          });
          _this.placeSearch = new AMap.PlaceSearch({ city: "全国" });
          _this.map.on("click", this.mapClick);
          _this.map.on("mousemove", this.mapMousemove);
          _this.createIcon(); // 创建icon对象
          _this.contextMenu = new AMap.ContextMenu();
          //右键放大
          _this.contextMenu.addItem("删除", _this.deleteMarker, 0);
          _this.getDetAirport();
        })
        .catch((e) => {
          // console.log(e);
        });
    },
    // 地图点击事件
    mapClick(e) {
      let _this = this;
      console.log("地图点击事件", e);
      let isok = this.isFan(e.pixel);
      console.log(isok);
      if (isok) {
        this.routesPointObj = {
          coordinates: e.lnglat.lng + "," + e.lnglat.lat,
          index: this.fromData.routesPointList.length,
          useGlobalHeight: 0,
          ellipsoidHeight: this.fromData.routesConfig.globalHeight,
          height: this.fromData.routesConfig.globalHeight,
          useGlobalSpeed: 1,
          waypointSpeed: this.fromData.routesConfig.autoFlightSpeed,
          gimbalPitchAngle: -30,
          executeHeight: 88,
          useGlobalHeadingParam: 1,
          waypointHeadingMode: "followWayline",
          waypointHeadingPathMode: "followBadArc",
          routesPointActionList: [],
          isisPointInRing: false,
          superelevationHeight: 0,
        };
        this.fromData.routesPointList.push(this.routesPointObj);
        this.drawAllMarker();
      } else {
        this.$modal.msgWarning("航点不能超出安全区域！");
      }
    },
    // 创建图标
    createIcon() {
      this.icon1 = new AMap.Icon({
        size: new AMap.Size(35, 35),
        image: require("@/assets/images/mapicon/airport1.png"),
        imageSize: new AMap.Size(35, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.icon2 = new AMap.Icon({
        size: new AMap.Size(35, 35),
        image: require("@/assets/images/mapicon/airport2.png"),
        imageSize: new AMap.Size(35, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.icon3 = new AMap.Icon({
        size: new AMap.Size(35, 35),
        image: require("@/assets/images/mapicon/airport3.png"),
        imageSize: new AMap.Size(35, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });
    },
    // 加载所有航点
    drawAllMarker() {
      let _this = this;
      this.isLineRing();
      // console.log("this.fromData.routesPointList",this.fromData.routesPointList)
      this.markerList.forEach((item) => {
        item.marker.remove();
      });
      this.markerList = [];
      this.fromData.routesPointList.forEach((item, index) => {
        item.index = index;
        let icon = null;
        if (item.coordinates == this.routesPointObj.coordinates) {
          icon = this.icon2;
        } else {
          if (item.isisPointInRing) {
            icon = this.icon3;
          } else {
            icon = this.icon1;
          }
        }
        if (item.isisPointInRing) {
          if (_this.timestamp) {
            let differ = new Date().getTime() - _this.timestamp;
            if (differ > 3000) {
              _this.$modal.msgWarning("请设置航点高度高于超高建筑高度！");
              _this.timestamp = new Date().getTime();
            }
          } else {
            _this.$modal.msgWarning("请设置航点高度高于超高建筑高度！");
            _this.timestamp = new Date().getTime();
          }
        }

        let labelContent = `<div class='labelContent'>${index + 1}</div>`;
        let marker = new AMap.Marker({
          extData: JSON.stringify(item),
          position: item.coordinates.split(","),
          icon: icon,
          offset: new AMap.Pixel(-15, -35),
          draggable: true,
          label: {
            direction: "center",
            content: labelContent,
            offset: new AMap.Pixel(0, 0),
          },
        });
        let obj = {
          marker: marker,
          isisPointInRing: item.isisPointInRing,
        };
        marker.on("click", this.markerClick);
        marker.on("dragging", this.markerDragging);
        marker.on("rightclick", this.markerRightClick);
        this.map.add(marker);
        this.markerList.push(obj);
      });
      this.drawLine();
    },
    // 删除航点
    deleteMarker() {
      console.log("this.routesPointObj", this.routesPointObj);
      this.contextMenu.close();
      this.fromData.routesPointList.splice(this.routesPointObj.index, 1);
      this.markerList[this.routesPointObj.index].marker.remove();
      this.markerList[this.routesPointObj.index].marker = null;
      this.markerList.splice(this.routesPointObj.index, 1);
      this.routesPointObj =
        this.fromData.routesPointList[this.fromData.routesPointList.length - 1];
      this.isDropLine = true;
      this.drawAllMarker();
      console.log(
        "this.fromData.routesPointList",
        this.fromData.routesPointList
      );
    },
    // 画航线
    drawLine() {
      let _this = this;
      if (!this.lineCanvas) {
        this.lineCanvas = document.createElement("canvas");
      }
      let onRender = function () {
        let size = _this.map.getSize();
        _this.lineCanvas.style.width = size.width + "px";
        _this.lineCanvas.style.height = size.height + "px";
        _this.lineCanvas.width = size.width;
        _this.lineCanvas.height = size.height;
        let ctx = _this.lineCanvas.getContext("2d");
        ctx.strokeStyle = "#fff";
        // #e8dc02 黄色
        ctx.lineWidth = 4;
        _this.fromData.routesPointList.forEach((item, index) => {
          if (index != 0) {
            ctx.beginPath();
            let pos = _this.map.lngLatToContainer(item.coordinates.split(","));
            let pos1 = _this.map.lngLatToContainer(
              _this.fromData.routesPointList[index - 1].coordinates.split(",")
            );
            ctx.moveTo(pos1.x, pos1.y);
            ctx.lineTo(pos.x, pos.y);
            if (
              item.isisPointInRing &&
              _this.fromData.routesPointList[index - 1].isisPointInRing
            ) {
              ctx.strokeStyle = "#e8dc02";
            } else {
              ctx.strokeStyle = "#fff";
            }
            ctx.stroke();
            ctx.closePath();
          }
        });
      };
      if (!this.lineCustomLayer) {
        this.lineCustomLayer = new AMap.CustomLayer(_this.lineCanvas, {
          zooms: [0, 20],
          zIndex: 30,
          alwaysRender: true,
        });
      }
      this.lineCustomLayer.render = onRender;
      this.map.addLayer(this.lineCustomLayer);
      this.lineCustomLayer.show();
      // distanceOfLine
      let path = _this.fromData.routesPointList.map((item) => {
        return item.coordinates.split(",");
      });
      this.routesLength = (
        AMap.GeometryUtil.distanceOfLine(path) / 1000
      ).toFixed(2);
      this.fromData.routesInfo.routeMileage =
        AMap.GeometryUtil.distanceOfLine(path).toFixed();
    },
    // 右键点击航点
    markerRightClick(e) {
      let data = JSON.parse(e.target.getExtData());
      this.routesPointObj = data;
      console.log("右键点击航点事件", data);
      this.contextMenu.open(this.map, e.lnglat);
    },
    // 航点点击事件
    markerClick(e) {
      let data = JSON.parse(e.target.getExtData());
      console.log("航点点击事件", data);
      this.routesPointObj = this.fromData.routesPointList[data.index];
      this.markerList.forEach((item, index) => {
        if (index == data.index) {
          item.marker.setIcon(this.icon2);
        } else {
          if (item.isisPointInRing) {
            item.marker.setIcon(this.icon3);
          } else {
            item.marker.setIcon(this.icon1);
          }
        }
      });
      this.isDropLine = false;
    },
    // 航点拖拽事件
    markerDragging(e) {
      let data = JSON.parse(e.target.getExtData());
      let isok = this.isFan(e.pixel);
      if (isok) {
        this.fromData.routesPointList[data.index].coordinates =
          e.lnglat.lng + "," + e.lnglat.lat;
        this.routesPointObj = this.fromData.routesPointList[data.index];
        this.drawAllMarker();
      } else {
        e.target.setPosition(
          this.fromData.routesPointList[data.index].coordinates.split(",")
        );
        if (this.timestamp) {
          let differ = new Date().getTime() - this.timestamp;
          if (differ > 1000) {
            this.$modal.msgWarning("航点不能超出安全区域！");
            this.timestamp = new Date().getTime();
          }
        } else {
          this.$modal.msgWarning("航点不能超出安全区域！");
          this.timestamp = new Date().getTime();
        }
      }
      // console.log("航点拖拽事件",data)
    },
    //地图鼠标移动事件
    mapMousemove(e) {
      let data = this.pointOnSegment(e.lnglat);
      if (data.isok) {
        // console.log("data",data)
        if (this.mouseText) {
          this.mouseText.hide();
        }
        if (this.addPointBtnObj) {
          this.addPointBtnObj.setPosition([data.lng, data.lat]);
          this.addPointBtnObj.show();
          this.addPointBtnObj.setExtData(JSON.stringify(data));
        } else {
          let icon = new AMap.Icon({
            size: new AMap.Size(20, 20),
            image: require("@/assets/images/mapicon/add.png"),
            imageSize: new AMap.Size(20, 20),
            imageOffset: new AMap.Pixel(0, 0),
          });
          this.addPointBtnObj = new AMap.Marker({
            extData: JSON.stringify(data),
            position: [data.lng, data.lat],
            icon: icon,
            offset: new AMap.Pixel(-10, -10),
            draggable: false,
          });
          this.addPointBtnObj.on("click", this.addPointBtnClick);
          this.map.add(this.addPointBtnObj);
          this.addPointBtnObj.show();
        }
      } else {
        if (this.addPointBtnObj) {
          this.addPointBtnObj.hide();
        }
        if (this.mouseText) {
          this.mouseText.setPosition(e.lnglat);
          this.mouseText.show();
        } else {
          this.mouseText = new AMap.Text({
            text: "鼠标左键点击添加航点",
            anchor: "center", // 设置文本标记锚点
            draggable: false,
            clickable: false,
            cursor: "pointer",
            style: {
              padding: "5px",
              "border-radius": "5px",
              "background-color": "rgb(66, 61, 66,.5)",
              width: "auto",
              "border-width": 0,
              "text-align": "center",
              "font-size": "14px",
              color: "#fff",
            },
            position: e.lnglat,
            offset: new AMap.Pixel(90, 10),
          });
          this.map.add(this.mouseText);
          this.mouseText.show();
        }

        if (this.mouseTimeOut) {
          clearTimeout(this.mouseTimeOut);
          this.mouseTimeOut = null;
        }
        this.mouseTimeOut = setTimeout(() => {
          if (this.mouseText) {
            this.mouseText.hide();
          }
        }, 1000);
      }
    },
    addPointBtnClick(e) {
      let data = JSON.parse(e.target.getExtData());
      console.log("addPointBtnClick", data);
      this.routesPointObj = {
        coordinates: data.lng + "," + data.lat,
        index: data.index,
        useGlobalHeight: 0,
        ellipsoidHeight: this.fromData.routesConfig.globalHeight,
        height: this.fromData.routesConfig.globalHeight,
        useGlobalSpeed: 1,
        waypointSpeed: this.fromData.routesConfig.autoFlightSpeed,
        gimbalPitchAngle: -30,
        executeHeight: 88,
        useGlobalHeadingParam: 1,
        waypointHeadingMode: "followWayline",
        waypointHeadingPathMode: "followBadArc",
        routesPointActionList: [],
        isisPointInRing: false,
        superelevationHeight: 0,
      };
      this.fromData.routesPointList.splice(data.index, 0, this.routesPointObj);
      this.addPointBtnObj.hide();
      this.drawAllMarker();
    },
    // 判断鼠标在那条线上
    pointOnSegment(lnglat) {
      let len = this.fromData.routesPointList.length;
      var m = this.map.getResolution() * 5;
      let obj = {
        isok: false,
        index: -1,
        lng: 0,
        lat: 0,
      };
      if (len > 1) {
        for (let i = 1; i < len; i++) {
          let pos1 =
            this.fromData.routesPointList[i - 1].coordinates.split(",");
          let pos2 = this.fromData.routesPointList[i].coordinates.split(",");
          let isok = AMap.GeometryUtil.isPointOnSegment(lnglat, pos1, pos2, m);
          if (isok) {
            obj.isok = true;
            obj.index = i;
            obj.lng = (Number(pos1[0]) + Number(pos2[0])) / 2;
            obj.lat = (Number(pos1[1]) + Number(pos2[1])) / 2;
          }
        }
      }
      return obj;
    },
    // 加载所有超高建筑
    loadAllGon() {
      this.sector.constructAreaList.forEach((item) => {
        let path = item.pathList.map((data) => {
          return [data.lng, data.lat];
        });
        let obj = {
          height: item.height,
          path: path,
        };
        this.superelevationList.push(obj);
        this.drawPolygon(this.map, path, item.height);
      });
    },
    // 使用canvas画面
    drawPolygon(map, path, height) {
      let _this = this;
      let canvas = document.createElement("canvas");
      let onRender = function () {
        let size = map.getSize();
        canvas.style.width = size.width + "px";
        canvas.style.height = size.height + "px";
        canvas.width = size.width;
        canvas.height = size.height;
        let ctx = canvas.getContext("2d");
        ctx.fillStyle = "rgba(98,123,242,.4)";
        ctx.strokeStyle = "rgba(98,123,242,1)";
        ctx.font = "bold 14px NSimSun";
        ctx.lineWidth = 2;
        ctx.beginPath();
        path.forEach((item) => {
          let pos = map.lngLatToContainer(item);
          ctx.lineTo(pos.x, pos.y);
        });
        ctx.closePath();
        ctx.stroke();
        ctx.fill();

        let center = _this.getPolygonCenter(path);
        let cen = map.lngLatToContainer([center.lng, center.lat]);
        ctx.fillStyle = "#fff";
        ctx.fillText(height + "米", cen.x - 10, cen.y + 5);
      };
      let zIndex = 30;
      if (height > this.sector.height) {
        zIndex = 30;
      } else {
        zIndex = 10;
      }
      let gon = new AMap.CustomLayer(canvas, {
        zooms: [10, 20],
        zIndex: zIndex,
        alwaysRender: true,
      });
      gon.render = onRender; // 将自定义的 render 方法挂在自定义图层的 render 属性上
      map.addLayer(gon);
      gon.show();
    },

    // 获取多边形中心坐标
    getPolygonCenter(lnglatarr) {
      var total = lnglatarr.length;
      var X = 0,
        Y = 0,
        Z = 0;
      lnglatarr.forEach(function (lnglat) {
        var lng = (lnglat[0] * Math.PI) / 180;
        var lat = (lnglat[1] * Math.PI) / 180;
        var x, y, z;
        x = Math.cos(lat) * Math.cos(lng);
        y = Math.cos(lat) * Math.sin(lng);
        z = Math.sin(lat);
        X += x;
        Y += y;
        Z += z;
      });
      X = X / total;
      Y = Y / total;
      Z = Z / total;

      var Lng = Math.atan2(Y, X);
      var Hyp = Math.sqrt(X * X + Y * Y);
      var Lat = Math.atan2(Z, Hyp);
      return { lng: (Lng * 180) / Math.PI, lat: (Lat * 180) / Math.PI };
    },

    // 地图扇形安全区域
    drawFan(longitude, latitude, startAngle, includeAngle, radiusList) {
      let _this = this;
      let fanCanvas = document.createElement("canvas");
      let onRender = function () {
        let size = _this.map.getSize();
        fanCanvas.style.width = size.width + "px";
        fanCanvas.style.height = size.height + "px";
        fanCanvas.width = size.width;
        fanCanvas.height = size.height;
        let ctx = fanCanvas.getContext("2d");
        ctx.fillStyle = "rgba(254,111,111,.5)";
        ctx.strokeStyle = "rgba(254,111,111,1)";
        ctx.lineWidth = 2;
        ctx.font = "bold 18px NSimSun";
        let pos = _this.map.lngLatToContainer([longitude, latitude]);
        let Angle = startAngle;

        radiusList.forEach((item) => {
          if (Angle - startAngle >= 360) {
            return;
          }
          ctx.fillStyle = "rgba(254,111,111,.4)";
          ctx.strokeStyle = "rgba(254,111,111,1)";
          var newLngLat = _this.azimuth_offset(
            longitude,
            latitude,
            0,
            Number(item.radius)
          ); //距离点的坐标
          let newPos = _this.map.lngLatToContainer(newLngLat);
          let radiusCtx = Math.sqrt(
            (pos.x - newPos.x) * (pos.x - newPos.x) +
              (pos.y - newPos.y) * (pos.y - newPos.y)
          );
          //绘制图形
          ctx.beginPath();
          if (includeAngle == 360) {
            ctx.moveTo(newPos.x, newPos.y);
          } else {
            ctx.moveTo(pos.x, pos.y);
          }
          ctx.arc(
            pos.x,
            pos.y,
            radiusCtx,
            (Math.PI * Angle) / 180,
            (Math.PI * (Angle + includeAngle)) / 180
          );
          ctx.closePath();
          ctx.stroke();
          ctx.fill();

          Angle += includeAngle;
        });
      };
      let customLayer = new AMap.CustomLayer(fanCanvas, {
        zooms: [5, 20],
        zIndex: 20,
        alwaysRender: true,
      });
      customLayer.render = onRender; // 将自定义的 render 方法挂在自定义图层的 render 属性上
      _this.map.addLayer(customLayer);
    },
    /**************************************************************************
     *从指定的原点出发，偏移输入角度后，向此方向延伸输入距离，返回此时的位置
     *origin_lon：原点经度
     *origin_lat：原点纬度
     *azimuth：偏移角度
     *distance：延伸距离
     *ret_lon:返回位置的经度
     *ret_lat:返回位置的纬度
     **************************************************************************/
    azimuth_offset(origin_lon, origin_lat, azimuth, distance) {
      var lonlat = [0.0, 0.0];
      if (azimuth != null && distance > 0) {
        lonlat[0] =
          origin_lon +
          (distance * Math.sin((azimuth * Math.PI) / 180) * 180) /
            (Math.PI * 6371229 * Math.cos((origin_lat * Math.PI) / 180));
        lonlat[1] =
          origin_lat +
          (distance * Math.cos((azimuth * Math.PI) / 180)) /
            ((Math.PI * 6371229) / 180);
      } else {
        lonlat[0] = origin_lon;
        lonlat[1] = origin_lat;
      }
      return lonlat;
    },
    // 判断是否点击在安全区域,lnglat 点击经纬度，pixel点击像素位置
    isFan(pixel) {
      let _this = this;
      // 中心点像素位置
      let pos = _this.map.lngLatToContainer([
        _this.airport.longitude,
        _this.airport.latitude,
      ]);
      //点击点和中心点的像素距离
      let radius = Math.sqrt(
        (pos.x - pixel.x) * (pos.x - pixel.x) +
          (pos.y - pixel.y) * (pos.y - pixel.y)
      );
      //对边 sin
      let sin = (pixel.y - pos.y) / radius;
      let angle = Math.round((Math.asin(sin) * 180) / Math.PI);
      if (this.sector.areaNum == 1) {
        var newLngLat = _this.azimuth_offset(
          _this.airport.longitude,
          _this.airport.latitude,
          0,
          _this.sector.areaList[0].radius
        ); //距离点的坐标
        let newPos = _this.map.lngLatToContainer(newLngLat);
        let radiusCtx = Math.sqrt(
          (pos.x - newPos.x) * (pos.x - newPos.x) +
            (pos.y - newPos.y) * (pos.y - newPos.y)
        );
        if (radiusCtx > radius) {
          return true;
        } else {
          return false;
        }
      } else {
        let area = _this.ofArea(pos, pixel); //属于哪个区域，1下右，2下左，3上左，4上右
        let startAngle = _this.sector.tilt - 90;
        let includeAngle = 360 / _this.sector.areaNum;
        if (area == 1) {
          angle = angle;
        } else if (area == 2) {
          angle = 90 - angle + 90;
        } else if (area == 3) {
          angle = 180 - angle;
        } else {
          angle = 270 + (90 + angle);
        }
        let isok = false;
        for (let i = 0; i < _this.sector.areaNum; i++) {
          if (startAngle <= 0) {
            let start = 360 + startAngle;
            let end = start + includeAngle;
            if (end >= 360) {
              if (
                (angle >= start && angle <= 360) ||
                (angle >= 0 && angle <= end - 360)
              ) {
                var newLngLat = _this.azimuth_offset(
                  _this.airport.longitude,
                  _this.airport.latitude,
                  0,
                  _this.sector.areaList[i].radius
                ); //距离点的坐标
                let newPos = _this.map.lngLatToContainer(newLngLat);
                let radiusCtx = Math.sqrt(
                  (pos.x - newPos.x) * (pos.x - newPos.x) +
                    (pos.y - newPos.y) * (pos.y - newPos.y)
                );
                if (radiusCtx > radius) {
                  isok = true;
                }
              }
            } else {
              if (angle >= start && angle <= end) {
                var newLngLat = _this.azimuth_offset(
                  _this.airport.longitude,
                  _this.airport.latitude,
                  0,
                  _this.sector.areaList[i].radius
                ); //距离点的坐标
                let newPos = _this.map.lngLatToContainer(newLngLat);
                let radiusCtx = Math.sqrt(
                  (pos.x - newPos.x) * (pos.x - newPos.x) +
                    (pos.y - newPos.y) * (pos.y - newPos.y)
                );
                if (radiusCtx > radius) {
                  isok = true;
                }
              }
            }
            startAngle += includeAngle;
          } else {
            let start = startAngle;
            let end = start + includeAngle;
            if (end >= 360) {
              if (
                (angle >= start && angle <= 360) ||
                (angle >= 0 && angle <= end - 360)
              ) {
                var newLngLat = _this.azimuth_offset(
                  _this.airport.longitude,
                  _this.airport.latitude,
                  0,
                  _this.sector.areaList[i].radius
                ); //距离点的坐标
                let newPos = _this.map.lngLatToContainer(newLngLat);
                let radiusCtx = Math.sqrt(
                  (pos.x - newPos.x) * (pos.x - newPos.x) +
                    (pos.y - newPos.y) * (pos.y - newPos.y)
                );
                if (radiusCtx > radius) {
                  isok = true;
                }
              }
            } else {
              if (angle >= start && angle <= end) {
                var newLngLat = _this.azimuth_offset(
                  _this.airport.longitude,
                  _this.airport.latitude,
                  0,
                  _this.sector.areaList[i].radius
                ); //距离点的坐标
                let newPos = _this.map.lngLatToContainer(newLngLat);
                let radiusCtx = Math.sqrt(
                  (pos.x - newPos.x) * (pos.x - newPos.x) +
                    (pos.y - newPos.y) * (pos.y - newPos.y)
                );
                if (radiusCtx > radius) {
                  isok = true;
                }
              }
            }
            startAngle += includeAngle;
          }
        }
        return isok;
      }
    },
    // 判断线跟面是否交叉
    isLineRing() {
      let len = this.fromData.routesPointList.length;
      for (let i = 0; i < len; i++) {
        this.superelevationList.forEach((item) => {
          if (i - 1 >= 0) {
            let isok = AMap.GeometryUtil.doesLineRingIntersect(
              [
                this.fromData.routesPointList[i].coordinates.split(","),
                this.fromData.routesPointList[i - 1].coordinates.split(","),
              ],
              item.path
            );
            let height1 = this.fromData.routesPointList[i].height;
            let height2 = this.fromData.routesPointList[i - 1].height;
            if (
              (height1 < Number(item.height) ||
                height2 < Number(item.height)) &&
              isok
            ) {
              this.fromData.routesPointList[i].isisPointInRing = true;
              this.fromData.routesPointList[i - 1].isisPointInRing = true;
              this.fromData.routesPointList[i].superelevationHeight = Number(
                item.height
              );
              this.fromData.routesPointList[i - 1].superelevationHeight =
                Number(item.height);
            } else {
              this.fromData.routesPointList[i].isisPointInRing = false;
            }
          } else {
            let isok = AMap.GeometryUtil.isPointInRing(
              this.fromData.routesPointList[i].coordinates.split(","),
              item.path
            );
            let height1 = this.fromData.routesPointList[i].height;
            if (Number(item.height) > height1 && isok) {
              this.fromData.routesPointList[i].isisPointInRing = true;
              this.fromData.routesPointList[i].superelevationHeight = Number(
                item.height
              );
            } else {
              this.fromData.routesPointList[i].isisPointInRing = false;
            }
          }
        });
      }
    },
    ofArea(pos, pixel) {
      if (pixel.x - pos.x > 0) {
        if (pixel.y - pos.y > 0) {
          return 1;
        } else {
          return 4;
        }
      } else {
        if (pixel.y - pos.y > 0) {
          return 2;
        } else {
          return 3;
        }
      }
    },

    // 控制搜索空是否显示
    ShowAddressSearch() {
      if (this.isShowAddressSearch) {
        this.isShowAddressSearch = false;
        this.addressSearch = "";
        this.addressOptions = [];
        if (this.addressMarker) {
          this.addressMarker.remove();
          this.addressMarker = null;
        }
      } else {
        this.isShowAddressSearch = true;
      }
    },
    // 地址选择
    addressSelect(val) {
      // console.log(val)
      let index = this.addressOptions.findIndex((item) => item.id == val);
      this.zoom = 12;
      this.map.setZoomAndCenter(this.zoom, [
        this.addressOptions[index].location.lng,
        this.addressOptions[index].location.lat,
      ]);
      if (this.addressMarker) {
        this.addressMarker.remove();
        this.addressMarker = null;
      }
      const icon = new AMap.Icon({
        size: new AMap.Size(24, 24),
        image: require("@/assets/images/mapicon/a16.png"),
        imageSize: new AMap.Size(24, 24),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.addressMarker = new AMap.Marker({
        position: [
          this.addressOptions[index].location.lng,
          this.addressOptions[index].location.lat,
        ],
        icon: icon,
        offset: new AMap.Pixel(-12, -12),
      });
      this.map.add(this.addressMarker);
    },
    // 地址搜索
    remoteMethod(val) {
      let _this = this;
      if (val !== "") {
        this.addressSearchLoading = true;
        this.placeSearch.search(val, (status, result) => {
          if (status == "complete" && result.poiList) {
            _this.addressSearchLoading = false;
            _this.addressOptions = result.poiList.pois;
          }
        });
      } else {
        this.addressOptions = [];
      }
    },
    //切换地图图层
    switchLayer() {
      if (!this.satellite) {
        this.satellite = new AMap.TileLayer.Satellite(); //卫星图层
      }
      if (this.isaddLayer) {
        this.isaddLayer = false;
        this.map.removeLayer(this.satellite);
      } else {
        this.isaddLayer = true;
        this.map.addLayer(this.satellite);
      }
    },
    // 放大图层
    addZoom() {
      if (this.zoom < 20) {
        this.zoom = this.zoom + 2;
        this.map.setZoom(this.zoom);
      } else {
        this.$modal.msgWarning("不能再放大了");
      }
    },
    // 缩小图层
    reduceZoom() {
      if (this.zoom > 6) {
        this.zoom = this.zoom - 2;
        this.map.setZoom(this.zoom);
      } else {
        this.$modal.msgWarning("不能再缩小了");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  background: #f5f5f5;
  position: relative;
}
.w-map {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(73, 68, 78, 0.2);
}

.w-right {
  position: absolute;
  width: 300px;
  height: calc(100% - 80px);
  top: 40px;
  right: 40px;
  background: url("../../../assets/images/plan_bg.png");
  background-size: 100% 100%;
  opacity: 0.9;
  border-radius: 10px;
  padding: 20px;
  color: #fff;
  .w-line-name {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 700;
    i {
      cursor: pointer;
      font-size: 18px;
    }
  }
  .w-info {
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    div {
      width: 32%;
      height: 50px;
      background: #1f1d24e6;
      border-radius: 5px;
      overflow: hidden;
      p {
        margin: 0;
        height: 25px;
        line-height: 25px;
        text-align: center;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
  .w-switch {
    width: 80%;
    height: 30px;
    font-size: 13px;
    display: flex;
    margin: 0 auto;
    div {
      width: 50%;
      height: 30px;
      text-align: center;
      line-height: 30px;
      cursor: pointer;
    }
    div:nth-child(1) {
      border: 1px solid #fff;
      border-radius: 15px 0 0 15px;
      border-right: none;
    }
    div:nth-child(2) {
      border: 1px solid #fff;
      border-radius: 0 15px 15px 0;
      border-left: none;
    }
    .w-active {
      background: #fff;
      color: #000;
    }
  }
  .w-line-info {
    width: 100%;
    height: calc(100% - 200px);
    overflow-x: hidden;
    overflow-y: auto;
    padding: 20px 0;
    .w-carousel {
      display: flex;
      width: 80%;
      height: 50px;
      margin: 0 auto;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #fff;
      i {
        cursor: pointer;
        padding: 10px;
      }
    }
    .w-line-info-box {
      width: 100%;
      height: calc(100% - 50px);
      overflow-x: hidden;
      overflow-y: auto;
    }
    .w-cell {
      width: 100%;
      padding: 5px 0;
      .w-title {
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
        .w-title-left {
          width: 50%;
          height: 30px;
          display: flex;
          align-items: center;
          span {
            margin-left: 5px;
          }
          i {
            font-size: 14px;
            cursor: pointer;
          }
        }
        .w-title-right {
          width: 50%;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          span {
            margin-right: 5px;
          }
          i {
            font-size: 14px;
            cursor: pointer;
          }
        }
      }
      .w-content {
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .w-content-img {
          width: 20px;
          height: 20px;
        }
        .w-content-slider {
          width: calc(100% - 70px);
          height: 40px;
        }
      }
    }
    .w-action {
      width: 100%;
    }
  }
  .w-btn {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.w-right-tool {
  position: absolute;
  width: 45px;
  right: 360px;
  bottom: 40px;
  display: flex;
  flex-direction: column;
  img {
    width: 45px;
    height: 45px;
    margin-top: 10px;
    cursor: pointer;
  }
}
.w-right-search {
  position: absolute;
  width: 260px;
  height: 40px;
  right: 420px;
  bottom: 207px;
  // background: #fff;
}
::v-deep {
  .amap-marker-label {
    background: none;
    color: #fff;
    border: none;
    font-weight: 700;
    font-size: 13px;
    cursor: pointer;
  }
  // .w-line-info {
  ::-webkit-scrollbar {
    display: none;
  }
  // }
}
</style>
