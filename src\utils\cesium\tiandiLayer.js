const tiandiKey = "4371a7bcd330fbad27256da9e9625526"; 
let ishttps = 'https:' == document.location.protocol ? true: false;
// 天地图矢量
export const tiandiVec_w = new Cesium.WebMapTileServiceImageryProvider({
    url: (ishttps? 'https': 'http') +'://{s}.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=' + tiandiKey,
    layer:'vec',
    style:'default',
    format:'tiles',
    tileMatrixSetID:'w',
    subdomains:['t0','t1','t2','t3','t4','t5','t6','t7'],
    credit:new Cesium.Credit('天地图矢量'),
    maximumLevel:18
})
// 天地图矢量标注
export const tiandiVav_w = new Cesium.WebMapTileServiceImageryProvider({
    url: (ishttps? 'https': 'http') + '://{s}.tianditu.gov.cn/cav_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=' + tiandiKey,
    layer:'cav',
    style:'default',
    format:'tiles',
    tileMatrixSetID:'w',
    subdomains:['t0','t1','t2','t3','t4','t5','t6','t7'],
    credit:new Cesium.Credit('天地图矢量标注'),
    maximumLevel:18
})



// 天地图影像
export const tiandiImg_w = new Cesium.WebMapTileServiceImageryProvider({
    url: (ishttps? 'https': 'http') + '://{s}.tianditu.gov.cn/img_w/wmts?tk=' + tiandiKey,
    layer:'img',
    style:'default',
    format:'tiles',
    tileMatrixSetID:'w',
    subdomains:['t0','t1','t2','t3','t4','t5','t6','t7'],
    credit:new Cesium.Credit('天地图影像'),
    maximumLevel:18
})
// 天地图影像标注
export const tiandiCia_w = new Cesium.WebMapTileServiceImageryProvider({
    url: (ishttps? 'https': 'http') + '://{s}.tianditu.gov.cn/cia_w/wmts?tk=' + tiandiKey,
    layer:'cia',
    style:'default',
    format:'tiles',
    tileMatrixSetID:'w',
    subdomains:['t0','t1','t2','t3','t4','t5','t6','t7'],
    credit:new Cesium.Credit('天地图影像标注'),
    maximumLevel:18
})