<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="无人机管理" name="first" class="w-tab"></el-tab-pane>
      <el-tab-pane label="电池管理" name="second"> </el-tab-pane>
    </el-tabs>
    <el-form
      v-show="activeName == 'first'"
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="设备名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入设备名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="绑定时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd(0)"
          v-hasPermi="['notroutes:info:add']"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-table
      v-show="activeName == 'first'"
      v-loading="loading"
      :data="tableList"
    >
      <el-table-column label="无人机名称" align="center" prop="name" />
      <el-table-column label="无人机型号" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.device_type" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column
        label="飞控序列号"
        align="center"
        prop="sn"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="绑定时间"
        align="center"
        prop="createTime"
        width="180"
      />
      <el-table-column
        label="责任人"
        align="center"
        prop="dutyName"
        width="180"
      ></el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['device:DroneInfo:edit']"
            >修改</el-button
          >
          <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleDet(scope.row)"
              >详情</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0 && activeName == 'first'"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-table
      v-loading="bloading"
      :data="batteryList"
      v-show="activeName == 'second'"
    >
      <el-table-column
        label="最后接入无人机名称"
        align="center"
        prop="lastAccessDroneName"
      >
      </el-table-column>
      <el-table-column
        label="最后接入机型"
        align="center"
        prop="lastAccessDroneType"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.device_type"
            :value="scope.row.lastAccessDroneType"
          />
        </template>
      </el-table-column>
      <el-table-column label="电池序列号" align="center" prop="sn">
      </el-table-column>
      <el-table-column
        label="出厂容量（mAh）"
        align="center"
        prop="originalCapacity"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="当前容量（mAh）"
        align="center"
        prop="currentCapacity"
        width="180"
      />
      <el-table-column
        label="当前循环次数"
        align="center"
        prop="loopTimes"
        width="180"
      ></el-table-column>
      <el-table-column
        label="最后使用时间"
        align="center"
        prop="lastUseTime"
        width="180"
      ></el-table-column>
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleDet(scope.row)"
                >详情</el-button>
              </template>
            </el-table-column> -->
    </el-table>

    <pagination
      v-show="btotal > 0 && activeName == 'second'"
      :total="btotal"
      :page.sync="batteryParams.pageNum"
      :limit.sync="batteryParams.pageSize"
      @pagination="getBatteryList"
    />

    <el-dialog title="编辑" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="无人机名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入无人机名称" />
        </el-form-item>
        <el-form-item label="飞控序列号" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入飞控序列号" />
        </el-form-item>
        <el-form-item label="无人机型号">
          <el-select
            v-model="form.type"
            placeholder="请选择无人机型号"
            @change="$forceUpdate()"
          >
            <el-option
              v-for="(item, index) in dict.type.device_type"
              :key="item.code"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { uvaList, editUva, batteryList, addUva } from "@/api/command/uva";

export default {
  name: "uva",
  dicts: ["device_type"],

  data() {
    return {
      // 遮罩层
      loading: false,
      bloading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 字典表格数据
      tableList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
      },
      batteryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "无人机名称不能为空", trigger: "blur" },
        ],
        sn: [{ required: true, message: "请输入飞控序列号", trigger: "blur" }],
      },
      activeName: "first",
      batteryList: [],
      btotal: 0,
    };
  },
  created() {
    this.getList();
    this.getBatteryList();
  },
  methods: {
    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      uvaList(
        this.addDateRange(this.queryParams, this.dateRange, "CreateTime")
      ).then((response) => {
        console.log("无人机列表", response);
        this.tableList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getBatteryList() {
      this.bloading = true;
      batteryList(this.batteryParams).then((res) => {
        console.log("电池列表", res);
        this.batteryList = res.rows;
        this.btotal = res.total;
        this.bloading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        sn: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form.id = row.id;
      this.form.name = row.name;
      this.form.sn = row.sn;
      this.form.type = JSON.parse(JSON.stringify(row.type));
      this.open = true;
    },
    handleAdd(id) {
      this.reset();
      this.form.id = id;
      this.form.name = undefined;
      this.form.sn = undefined;
      this.form.name = undefined;
      this.open = true;
    },
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id == 0) {
            delete this.form.id;
            addUva(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          } else {
            editUva(this.form).then((response) => {
              this.$modal.msgSuccess("编辑成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleDet(row) {},
  },
};
</script>

<style lang="scss" scoped></style>
