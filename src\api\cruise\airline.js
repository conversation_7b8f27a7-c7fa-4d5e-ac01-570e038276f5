import request from '@/utils/request'

// 获取机场航线列表
export function routesList(query) {
    return request({
      url: '/routes/info/list',
      method: 'get',
      params: query
    })
}

// 删除航线
export function DeleteRoutes(id) {
  return request({
    url: '/routes/info/' + id,
    method: 'delete',
  })
}

// 添加航线
export function addRoutes(data) {
  return request({
      url: "/routes/info",
      method: "post",
      data
  })
}

// 获取航线详情
export function routesDet(id) {
  return request({
    url: '/routes/info/' + id,
    method: 'get'
  })
}

// 修改航线详情
export function putRoutes(data) {
  return request({
    url: '/routes/info',
    method: 'put',
    data
  })
}


// 获取无人机航线列表（无机场）
export function notAirportList(query) {
  return request({
    url: '/routes/info/notAirportList',
    method: 'get',
    params: query
  })
}

// 删除航线（无机场）
export function DeleteNotAirportRoutes(id) {
  return request({
    url: '/routes/info/notAirport/' + id,
    method: 'delete',
  })
}

// 添加航线（无机场）
export function addNotAirportRoutes(data) {
  return request({
      url: "/routes/info/notAirport",
      method: "post",
      data
  })
}

// 修改航线详情（无机场）
export function putNotAirportRoutes(data) {
  return request({
    url: '/routes/info/notAirport',
    method: 'put',
    data
  })
}

// 航线分享列表
export function getFlyByRoutesId(id) {
  return request({
    url: '/system/user/getFlyByRoutesId/' + id,
    method: 'get',
  })
}

// 航线分享
export function updateUserRoutes(query) {
  return request({
    url: '/system/user/updateUserRoutes',
    method: 'post',
    params: query
  })
}


export function uploadKmzWithRoute(data) {
  const formData = new FormData()
  formData.append('routesName', data.routesName)
  formData.append('file', data.file)
  
  return request({
    url: '/business/kmz/uploadKmz',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
