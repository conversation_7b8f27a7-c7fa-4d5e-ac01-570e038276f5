<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="任务名称" prop="taskName">
            <el-input
            v-model="queryParams.taskName"
            placeholder="请输入任务名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="航线名称" prop="routesName">
            <el-input
            v-model="queryParams.routesName"
            placeholder="请输入航线名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="机场" prop="airportId">
            <el-select
                v-model="queryParams.airportId"
                placeholder="请选择机场"
                clearable
            >
            <el-option v-for="item in airportList" :key="item.airportId" :label="item.airportName" :value="item.airportId"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
  
      <el-table v-loading="loading" :data="tableList">
        <el-table-column label="任务名称" align="center" prop="taskName" />
        <el-table-column label="任务描述" align="center" prop="taskDesc" :show-overflow-tooltip="true" />
        <el-table-column label="执行时间" align="center" prop="taskBeginTime" :show-overflow-tooltip="true" />
        <el-table-column label="关联航线" align="center" prop="routesName" :show-overflow-tooltip="true" />
        <el-table-column label="航线图" align="center">
          <template slot-scope="scope">
            <el-image
              class="table-td-thumb"
              :src="scope.row.routesPreviewUrl"
              :preview-src-list="[scope.row.routesPreviewUrl]"
            ></el-image>
          </template>
        </el-table-column>
        <el-table-column label="所属机场" align="center" prop="airportName" width="180"></el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="toActual(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getTableData"
      />
    </div>
  </template>
  
  <script>
  import { listAirport } from "@/api/cruise/airdrome";
  import { recordTask } from "@/api/cruise/monitor";
  export default {
    name: "cruise_record",
    data() {
      return {
        // 遮罩层
        loading: false,
        // 总条数
        total: 0,
        tableList: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          taskStatus: 1,
          taskName: undefined,
          routesName: undefined,
          airportId: undefined
        },
        airportList: []
      };
    },
    created() {
        this.getAirport()
        this.getTableData()
    },
    methods: {
        toActual(data){
            this.$router.push("/cruise/record-det/task/" + data.id)
        },
        getAirport(){
            listAirport({pageNum: 1,pageSize: 999}).then(res => {
                console.log("机场列表",res)
                this.airportList = res.rows || []
            })
        },
        getTableData(){
            this.loading = true
            recordTask(this.queryParams).then(res => {
                console.log("任务列表",res)
                this.tableList = res.rows || []
                this.total = res.total
                this.loading = false
            })
        },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getTableData()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
    }
  };
  </script>

  <style lang="scss" scoped>
  .table-td-thumb {
      display: block;
      margin: auto;
      width: 50px;
  }
  </style>