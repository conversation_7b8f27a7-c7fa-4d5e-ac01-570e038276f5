<template>
  <div class="share-container">
    <div class="w-title" v-if="!isExpire">
      {{ liveDet ? liveDet.typeName + " - " + liveDet.nickName : "-" }}
    </div>
    <video
      v-if="!isExpire"
      class="UvaVideo"
      autoplay
      controls
      id="videoElement"
    ></video>
    <div v-if="!isExpire" @click="playVideo" class="w-start-btn">播 放</div>
    <div v-if="!isExpire" @click="closeVideo" class="w-end-btn">结 束</div>
    <!-- <el-button @click="playVideo" type="primary" size="small">播放</el-button> -->
    <!-- <el-button @click="closeVideo" type="primary" size="small">结束</el-button> -->
    <div class="w-expire" v-if="isExpire">
      <img src="@/assets/images/expire.png" />
      <p>您的链接已过期！</p>
    </div>
  </div>
</template>

<script>
import flvjs from "flv.js";
import { encrypt, decrypt } from "@/utils/crypto";
export default {
  name: "Dict",
  data() {
    return {
      flvPlayer: null,
      liveDet: null,
      isExpire: true,
    };
  },
  created() {
    console.log(this.$route.query);
    let data = decrypt(this.$route.query.data);
    console.log(data);
    this.liveDet = data;
    let timestamp = new Date().getTime();
    if (timestamp > data.timestamp) {
      this.isExpire = true;
    } else {
      this.isExpire = false;
    }
    // let obj = {
    //     timestamp:1683596926102,
    //     sn: '1581F5FHD233E00D8B21',
    //     actionId: 99,
    //     nickName: '飞手2',
    //     typeName: 'M3E'
    // }
    // console.log("加密",encrypt(JSON.stringify(obj)))
    // console.log("解密",decrypt(encrypt(JSON.stringify(obj))))
    /**
     * data: {timestamp:1683596926102,sn: '1581F5FHD233E00D8B21', actionId: 99, nickName: '飞手2', typeName: 'M3E'}
     * timestamp 失效时间
     * sn 飞机sn
     * actionId 行动id
     * nickName 飞手名称
     * typeName 无人机型号
     */
  },
  methods: {
    closeVideo() {
      if (this.flvPlayer) {
        this.flvPlayer.pause();
        this.flvPlayer.unload();
        this.flvPlayer.detachMediaElement();
        this.flvPlayer.destroy();
        this.flvPlayer = null;
      }
    },
    playVideo() {
      this.closeVideo();
      let videoElement = document.getElementById("videoElement");
      let url = `http://192.168.1.99:8007/live/${this.liveDet.actionId}-${this.liveDet.sn}.live.flv`;
      this.flvPlayer = this.openAirdromeVideo(videoElement, url);
    },
    openAirdromeVideo(videoElement, url) {
      let _this = this;
      let flvPlayer = null;
      if (flvjs.isSupported()) {
        flvPlayer = flvjs.createPlayer(
          {
            type: "flv",
            url: url,
            isLive: true,
            hasAudio: false,
            hasVideo: true,
            cors: true,
            withCredentials: false,
          },
          {
            enableWorker: false, //不启用分离线程
            lazyLoadMaxDuration: 3 * 60,
            seekType: "range",
          }
        );
        flvPlayer.attachMediaElement(videoElement);
        flvPlayer.load();

        flvPlayer.on(flvjs.Events.LOADING_COMPLETE, (res) => {
          console.log("直播已结束", res);
          flvPlayer.pause();
          flvPlayer.unload();
          flvPlayer.detachMediaElement();
          flvPlayer.destroy();
          flvPlayer = null;
          this.$modal.msg("视频已结束");
        });
        flvPlayer.on(
          flvjs.Events.ERROR,
          (errorType, errorDetail, errorInfo) => {
            flvPlayer.pause();
            flvPlayer.unload();
            flvPlayer.detachMediaElement();
            flvPlayer.destroy();
            flvPlayer = null;
            _this.$modal.msgError("未请求到视频！");
          }
        );
      }
      return flvPlayer;
    },
  },
  beforeDestroy() {
    this.closeVideo();
  },
};
</script>

<style lang="scss" scoped>
.share-container {
  width: 100%;
  height: 100%;
  position: relative;
}
.UvaVideo {
  width: 100%;
  height: auto;
}

.w-title {
  width: 100%;
  text-align: center;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
}

.w-start-btn {
  width: 60%;
  height: 40px;
  line-height: 40px;
  margin: 0 auto;
  margin-top: 20px;
  background: #66b1ff;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-size: 16px;
}
.w-end-btn {
  width: 60%;
  height: 40px;
  line-height: 40px;
  margin: 0 auto;
  margin-top: 20px;
  background: #f56c6c;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-size: 16px;
}

.w-expire {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img {
    width: 30%;
  }
  p {
    text-align: center;
    font-size: 18px;
  }
}
</style>
