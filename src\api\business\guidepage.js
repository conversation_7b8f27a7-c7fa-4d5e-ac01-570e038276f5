import request from '@/utils/request'

// 查询引导页管理列表
export function listGuidepage(query) {
  return request({
    url: '/business/guidepage/list',
    method: 'get',
    params: query
  })
}

// 查询引导页管理详细
export function getGuidepage(id) {
  return request({
    url: '/business/guidepage/' + id,
    method: 'get'
  })
}

// 新增引导页管理
export function addGuidepage(data) {
  return request({
    url: '/business/guidepage',
    method: 'post',
    data: data
  })
}

// 修改引导页管理
export function updateGuidepage(data) {
  return request({
    url: '/business/guidepage',
    method: 'put',
    data: data
  })
}

// 删除引导页管理
export function delGuidepage(id) {
  return request({
    url: '/business/guidepage/' + id,
    method: 'delete'
  })
}
