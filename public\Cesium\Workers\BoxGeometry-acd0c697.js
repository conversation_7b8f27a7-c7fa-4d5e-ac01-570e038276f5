/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.95
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-d3d3b2a9","./Matrix2-73789715","./RuntimeError-4f8ec8a2","./ComponentDatatype-e7fbe225","./defaultValue-97284df2","./GeometryAttribute-fd1d7e90","./GeometryAttributes-734a3446","./GeometryOffsetAttribute-59b14f45","./VertexFormat-9886cb81"],(function(t,e,n,a,r,i,o,m,u,s){"use strict";const y=new n.Cartesian3;function p(t){const e=(t=i.defaultValue(t,i.defaultValue.EMPTY_OBJECT)).minimum,a=t.maximum,r=i.defaultValue(t.vertexFormat,s.VertexFormat.DEFAULT);this._minimum=n.Cartesian3.clone(e),this._maximum=n.Cartesian3.clone(a),this._vertexFormat=r,this._offsetAttribute=t.offsetAttribute,this._workerName="createBoxGeometry"}p.fromDimensions=function(t){const e=(t=i.defaultValue(t,i.defaultValue.EMPTY_OBJECT)).dimensions,a=n.Cartesian3.multiplyByScalar(e,.5,new n.Cartesian3);return new p({minimum:n.Cartesian3.negate(a,new n.Cartesian3),maximum:a,vertexFormat:t.vertexFormat,offsetAttribute:t.offsetAttribute})},p.fromAxisAlignedBoundingBox=function(t){return new p({minimum:t.minimum,maximum:t.maximum})},p.packedLength=2*n.Cartesian3.packedLength+s.VertexFormat.packedLength+1,p.pack=function(t,e,a){return a=i.defaultValue(a,0),n.Cartesian3.pack(t._minimum,e,a),n.Cartesian3.pack(t._maximum,e,a+n.Cartesian3.packedLength),s.VertexFormat.pack(t._vertexFormat,e,a+2*n.Cartesian3.packedLength),e[a+2*n.Cartesian3.packedLength+s.VertexFormat.packedLength]=i.defaultValue(t._offsetAttribute,-1),e};const x=new n.Cartesian3,c=new n.Cartesian3,f=new s.VertexFormat,l={minimum:x,maximum:c,vertexFormat:f,offsetAttribute:void 0};let A;p.unpack=function(t,e,a){e=i.defaultValue(e,0);const r=n.Cartesian3.unpack(t,e,x),o=n.Cartesian3.unpack(t,e+n.Cartesian3.packedLength,c),m=s.VertexFormat.unpack(t,e+2*n.Cartesian3.packedLength,f),u=t[e+2*n.Cartesian3.packedLength+s.VertexFormat.packedLength];return i.defined(a)?(a._minimum=n.Cartesian3.clone(r,a._minimum),a._maximum=n.Cartesian3.clone(o,a._maximum),a._vertexFormat=s.VertexFormat.clone(m,a._vertexFormat),a._offsetAttribute=-1===u?void 0:u,a):(l.offsetAttribute=-1===u?void 0:u,new p(l))},p.createGeometry=function(t){const a=t._minimum,s=t._maximum,p=t._vertexFormat;if(n.Cartesian3.equals(a,s))return;const x=new m.GeometryAttributes;let c,f;if(p.position&&(p.st||p.normal||p.tangent||p.bitangent)){if(p.position&&(f=new Float64Array(72),f[0]=a.x,f[1]=a.y,f[2]=s.z,f[3]=s.x,f[4]=a.y,f[5]=s.z,f[6]=s.x,f[7]=s.y,f[8]=s.z,f[9]=a.x,f[10]=s.y,f[11]=s.z,f[12]=a.x,f[13]=a.y,f[14]=a.z,f[15]=s.x,f[16]=a.y,f[17]=a.z,f[18]=s.x,f[19]=s.y,f[20]=a.z,f[21]=a.x,f[22]=s.y,f[23]=a.z,f[24]=s.x,f[25]=a.y,f[26]=a.z,f[27]=s.x,f[28]=s.y,f[29]=a.z,f[30]=s.x,f[31]=s.y,f[32]=s.z,f[33]=s.x,f[34]=a.y,f[35]=s.z,f[36]=a.x,f[37]=a.y,f[38]=a.z,f[39]=a.x,f[40]=s.y,f[41]=a.z,f[42]=a.x,f[43]=s.y,f[44]=s.z,f[45]=a.x,f[46]=a.y,f[47]=s.z,f[48]=a.x,f[49]=s.y,f[50]=a.z,f[51]=s.x,f[52]=s.y,f[53]=a.z,f[54]=s.x,f[55]=s.y,f[56]=s.z,f[57]=a.x,f[58]=s.y,f[59]=s.z,f[60]=a.x,f[61]=a.y,f[62]=a.z,f[63]=s.x,f[64]=a.y,f[65]=a.z,f[66]=s.x,f[67]=a.y,f[68]=s.z,f[69]=a.x,f[70]=a.y,f[71]=s.z,x.position=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f})),p.normal){const t=new Float32Array(72);t[0]=0,t[1]=0,t[2]=1,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=1,t[9]=0,t[10]=0,t[11]=1,t[12]=0,t[13]=0,t[14]=-1,t[15]=0,t[16]=0,t[17]=-1,t[18]=0,t[19]=0,t[20]=-1,t[21]=0,t[22]=0,t[23]=-1,t[24]=1,t[25]=0,t[26]=0,t[27]=1,t[28]=0,t[29]=0,t[30]=1,t[31]=0,t[32]=0,t[33]=1,t[34]=0,t[35]=0,t[36]=-1,t[37]=0,t[38]=0,t[39]=-1,t[40]=0,t[41]=0,t[42]=-1,t[43]=0,t[44]=0,t[45]=-1,t[46]=0,t[47]=0,t[48]=0,t[49]=1,t[50]=0,t[51]=0,t[52]=1,t[53]=0,t[54]=0,t[55]=1,t[56]=0,t[57]=0,t[58]=1,t[59]=0,t[60]=0,t[61]=-1,t[62]=0,t[63]=0,t[64]=-1,t[65]=0,t[66]=0,t[67]=-1,t[68]=0,t[69]=0,t[70]=-1,t[71]=0,x.normal=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:t})}if(p.st){const t=new Float32Array(48);t[0]=0,t[1]=0,t[2]=1,t[3]=0,t[4]=1,t[5]=1,t[6]=0,t[7]=1,t[8]=1,t[9]=0,t[10]=0,t[11]=0,t[12]=0,t[13]=1,t[14]=1,t[15]=1,t[16]=0,t[17]=0,t[18]=1,t[19]=0,t[20]=1,t[21]=1,t[22]=0,t[23]=1,t[24]=1,t[25]=0,t[26]=0,t[27]=0,t[28]=0,t[29]=1,t[30]=1,t[31]=1,t[32]=1,t[33]=0,t[34]=0,t[35]=0,t[36]=0,t[37]=1,t[38]=1,t[39]=1,t[40]=0,t[41]=0,t[42]=1,t[43]=0,t[44]=1,t[45]=1,t[46]=0,t[47]=1,x.st=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:t})}if(p.tangent){const t=new Float32Array(72);t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t[6]=1,t[7]=0,t[8]=0,t[9]=1,t[10]=0,t[11]=0,t[12]=-1,t[13]=0,t[14]=0,t[15]=-1,t[16]=0,t[17]=0,t[18]=-1,t[19]=0,t[20]=0,t[21]=-1,t[22]=0,t[23]=0,t[24]=0,t[25]=1,t[26]=0,t[27]=0,t[28]=1,t[29]=0,t[30]=0,t[31]=1,t[32]=0,t[33]=0,t[34]=1,t[35]=0,t[36]=0,t[37]=-1,t[38]=0,t[39]=0,t[40]=-1,t[41]=0,t[42]=0,t[43]=-1,t[44]=0,t[45]=0,t[46]=-1,t[47]=0,t[48]=-1,t[49]=0,t[50]=0,t[51]=-1,t[52]=0,t[53]=0,t[54]=-1,t[55]=0,t[56]=0,t[57]=-1,t[58]=0,t[59]=0,t[60]=1,t[61]=0,t[62]=0,t[63]=1,t[64]=0,t[65]=0,t[66]=1,t[67]=0,t[68]=0,t[69]=1,t[70]=0,t[71]=0,x.tangent=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:t})}if(p.bitangent){const t=new Float32Array(72);t[0]=0,t[1]=1,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=1,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=1,t[14]=0,t[15]=0,t[16]=1,t[17]=0,t[18]=0,t[19]=1,t[20]=0,t[21]=0,t[22]=1,t[23]=0,t[24]=0,t[25]=0,t[26]=1,t[27]=0,t[28]=0,t[29]=1,t[30]=0,t[31]=0,t[32]=1,t[33]=0,t[34]=0,t[35]=1,t[36]=0,t[37]=0,t[38]=1,t[39]=0,t[40]=0,t[41]=1,t[42]=0,t[43]=0,t[44]=1,t[45]=0,t[46]=0,t[47]=1,t[48]=0,t[49]=0,t[50]=1,t[51]=0,t[52]=0,t[53]=1,t[54]=0,t[55]=0,t[56]=1,t[57]=0,t[58]=0,t[59]=1,t[60]=0,t[61]=0,t[62]=1,t[63]=0,t[64]=0,t[65]=1,t[66]=0,t[67]=0,t[68]=1,t[69]=0,t[70]=0,t[71]=1,x.bitangent=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:t})}c=new Uint16Array(36),c[0]=0,c[1]=1,c[2]=2,c[3]=0,c[4]=2,c[5]=3,c[6]=6,c[7]=5,c[8]=4,c[9]=7,c[10]=6,c[11]=4,c[12]=8,c[13]=9,c[14]=10,c[15]=8,c[16]=10,c[17]=11,c[18]=14,c[19]=13,c[20]=12,c[21]=15,c[22]=14,c[23]=12,c[24]=18,c[25]=17,c[26]=16,c[27]=19,c[28]=18,c[29]=16,c[30]=20,c[31]=21,c[32]=22,c[33]=20,c[34]=22,c[35]=23}else f=new Float64Array(24),f[0]=a.x,f[1]=a.y,f[2]=a.z,f[3]=s.x,f[4]=a.y,f[5]=a.z,f[6]=s.x,f[7]=s.y,f[8]=a.z,f[9]=a.x,f[10]=s.y,f[11]=a.z,f[12]=a.x,f[13]=a.y,f[14]=s.z,f[15]=s.x,f[16]=a.y,f[17]=s.z,f[18]=s.x,f[19]=s.y,f[20]=s.z,f[21]=a.x,f[22]=s.y,f[23]=s.z,x.position=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f}),c=new Uint16Array(36),c[0]=4,c[1]=5,c[2]=6,c[3]=4,c[4]=6,c[5]=7,c[6]=1,c[7]=0,c[8]=3,c[9]=1,c[10]=3,c[11]=2,c[12]=1,c[13]=6,c[14]=5,c[15]=1,c[16]=2,c[17]=6,c[18]=2,c[19]=3,c[20]=7,c[21]=2,c[22]=7,c[23]=6,c[24]=3,c[25]=0,c[26]=4,c[27]=3,c[28]=4,c[29]=7,c[30]=0,c[31]=1,c[32]=5,c[33]=0,c[34]=5,c[35]=4;const l=n.Cartesian3.subtract(s,a,y),A=.5*n.Cartesian3.magnitude(l);if(i.defined(t._offsetAttribute)){const e=f.length,n=t._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,a=new Uint8Array(e/3).fill(n);x.applyOffset=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}return new o.Geometry({attributes:x,indices:c,primitiveType:o.PrimitiveType.TRIANGLES,boundingSphere:new e.BoundingSphere(n.Cartesian3.ZERO,A),offsetAttribute:t._offsetAttribute})},p.getUnitBox=function(){return i.defined(A)||(A=p.createGeometry(p.fromDimensions({dimensions:new n.Cartesian3(1,1,1),vertexFormat:s.VertexFormat.POSITION_ONLY}))),A},t.BoxGeometry=p}));
