import request from '@/utils/request'

// 查询Wayline file information列表
export function listWaylinefile(query) {
  return request({
    url: '/business/waylinefile/list',
    method: 'get',
    params: query
  })
}

// 查询Wayline file information详细
export function getWaylinefile(id) {
  return request({
    url: '/business/waylinefile/' + id,
    method: 'get'
  })
}

// 新增Wayline file information
export function addWaylinefile(data) {
  return request({
    url: '/business/waylinefile',
    method: 'post',
    data: data
  })
}

// 修改Wayline file information
export function updateWaylinefile(data) {
  return request({
    url: '/business/waylinefile',
    method: 'put',
    data: data
  })
}

// 删除Wayline file information
export function delWaylinefile(id) {
  return request({
    url: '/business/waylinefile/' + id,
    method: 'delete'
  })
}
