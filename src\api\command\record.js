import request from '@/utils/request'

// 获取飞行记录
export function listRecord(params) {
    return request({
      url: '/plan/record/list',
      method: 'get',
      params
    })
}

// 获取飞行记录详情坐标信息
export function recordOsdDet(params) {
    return request({
      url: '/plan/record/osd/list',
      method: 'get',
      params
    })
}

// 获取飞行记录详情媒体信息
export function recordMediaDet(params) {
    return request({
      url: '/plan/record/media/list',
      method: 'get',
      params
    })
}

// 获取飞行记录详情基本信息
export function recordDet(id) {
    return request({
      url: '/plan/record/' + id,
      method: 'get',
    })
}

// 获取飞行记录删除
export function recordDelete(id) {
  return request({
    url: '/plan/record/' + id,
    method: 'delete',
  })
}