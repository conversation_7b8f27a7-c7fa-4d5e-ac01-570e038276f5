import request from '@/utils/request'

// 查询无人机设备列表
export function listDeviceInfo(query) {
  return request({
    url: '/business/deviceinfo/list',
    method: 'get',
    params: query
  })
}

// 查询无人机设备详细
export function getDeviceInfo(id) {
  return request({
    url: '/business/deviceinfo/' + id,
    method: 'get'
  })
}

// 新增无人机设备
export function addDeviceInfo(data) {
  return request({
    url: '/business/deviceinfo',
    method: 'post',
    data: data
  })
}

// 修改无人机设备
export function updateDeviceInfo(data) {
  return request({
    url: '/business/deviceinfo',
    method: 'put',
    data: data
  })
}

// 删除无人机设备
export function delDeviceInfo(id) {
  return request({
    url: '/business/deviceinfo/' + id,
    method: 'delete'
  })
}
