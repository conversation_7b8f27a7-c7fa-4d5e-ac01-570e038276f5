import request from '@/utils/request'

// 查询景点管理列表
export function listSpot(query) {
  return request({
    url: '/business/spot/list',
    method: 'get',
    params: query
  })
}

// 查询景点管理详细
export function getSpot(id) {
  return request({
    url: '/business/spot/' + id,
    method: 'get'
  })
}

// 新增景点管理
export function addSpot(data) {
  return request({
    url: '/business/spot',
    method: 'post',
    data: data
  })
}

// 修改景点管理
export function updateSpot(data) {
  return request({
    url: '/business/spot',
    method: 'put',
    data: data
  })
}

// 删除景点管理
export function delSpot(id) {
  return request({
    url: '/business/spot/' + id,
    method: 'delete'
  })
}

// 获取航线列表
export function listRoutes() {
  return request({
    url: '/routes/info/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 999
    }
  })
}

// 获取机场列表
export function listAirport() {
  return request({
    url: '/device/airport/volist',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 999
    }
  })
}
