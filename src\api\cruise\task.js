import request from '@/utils/request'

// 获取任务列表
export function TaskMonthTaskList(query) {
    return request({
      url: '/task/calendar/monthTask',
      method: 'get',
      params: query
    })
}

// 获取任务详情
export function TaskDet(id) {
  return request({
    url: '/task/calendar/' + id,
    method: 'get'
  })
}

// 添加任务
export function addTask(data) {
  return request({
      url: "/task/calendar",
      method: "post",
      data
  })
}

// 修改任务
export function editTask(data) {
  return request({
      url: "/task/calendar",
      method: "put",
      data
  })
}


//将任务拉入黑名单
export function removeTask(data){
  return request({
    url: "/task/blacklist",
    method: "post",
    data
  })
}

// 删除任务
export function deleteTask(id) {
  return request({
      url: '/task/calendar/' + id,
      method: 'delete',
  })
}