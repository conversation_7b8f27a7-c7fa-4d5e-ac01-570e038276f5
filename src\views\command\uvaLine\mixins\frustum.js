import CreateFrustum from "@/utils/cesium/cesiumFrustum"
export default {
  data() {
    return {
        Heading: 0,
        Pitch: 0,
        Roll: 0,
        Frustum: null,
        origin: null,
        
    }
  },
  methods: {
    editHeading(data,type){
        if(data == '-') {
            if(type == 1) {
                this.Heading--
            } else if(type == 2) {
                this.Pitch--
            } else {
                this.Roll--
            }
        } else {
            if(type == 1) {
                this.Heading++
            } else if(type == 2) {
                this.Pitch++
            } else {
                this.Roll++
            }
        }
        let headingPitchRoll = new Cesium.HeadingPitchRoll(this.Heading * Math.PI / 180,this.Pitch * Math.PI / 180,this.Roll * Math.PI / 180)
        let orientation = Cesium.Transforms.headingPitchRollQuaternion(this.origin,headingPitchRoll)
        this.Frustum.update(this.origin,orientation)
    },
    HeadingPitchRollChange(val){
        let headingPitchRoll = new Cesium.HeadingPitchRoll(this.Heading * Math.PI / 180,this.Pitch * Math.PI / 180,this.Roll * Math.PI / 180)
        let orientation = Cesium.Transforms.headingPitchRollQuaternion(this.origin,headingPitchRoll)
        this.Frustum.update(this.origin,orientation)
    },
    createFrustum(log,lat, height, Heading, Pitch, Roll) {
        // 创建视点
        let origin = Cesium.Cartesian3.fromDegrees(log, lat, height);
        // this.createPoint(origin);

        // 创建XYZ局部坐标轴 // X轴：红色，Y轴：绿色，Z轴：蓝色
        // this.createFrame(origin);

        // // 视角定位
        // this.viewer.camera.flyTo({
        //     destination: origin,
        // });

        // 确定相对于视点的旋转矩阵
        // let enu = Cesium.Transforms.eastNorthUpToFixedFrame(origin);
        // // console.log("enu",enu)
        // let rotation = Cesium.Matrix3.getRotation(enu, new Cesium.Matrix3());
        // // console.log("rotation",rotation)
        // let rot = Cesium.Matrix3.fromRotationY(30)
        // let orientation = Cesium.Quaternion.fromRotationMatrix(new Cesium.Matrix3());
        // let orientation = Cesium.Quaternion.fromRotationMatrix(rotation);
        // console.log("orientation",orientation)

    //     var e = this.properties.headingPitchRoll
    //     , t = this.getPositionCartesian3();
    //   if (t && e) {
    //       var i = C["Math"].toRadians(e.heading)
    //         , n = C["Math"].toRadians(e.pitch)
    //         , r = C["Math"].toRadians(e.roll)
    //         , a = new C["HeadingPitchRoll"](i,n,r);
    //       return C["Transforms"].headingPitchRollQuaternion(t, a)
    //   }
        
        let headingPitchRoll = new Cesium.HeadingPitchRoll(Heading * Math.PI / 180, Pitch * Math.PI / 180, Roll * Math.PI / 180)
        let orientation = Cesium.Transforms.headingPitchRollQuaternion(origin,headingPitchRoll)

        // 创建视锥体
        this.Frustum = new CreateFrustum({
            viewer: this.viewer,
            position: origin,
            orientation: orientation,
            fov: 30,
            near: 1,
            far: 200,
            aspectRatio: 300 / 400,
        });

        // 动态修改视锥体的姿态
        // let head = 0;
        // let pitch = 0;
        // let roll = 0;
        // let rot = orientation;
        // setInterval(()=>{
        //     // 绕Z轴旋转-航向
        //     head += 0.01;
        //     rot = Cesium.Matrix3.multiply(
        //         rotation,
        //         Cesium.Matrix3.fromRotationZ(head),
        //         new Cesium.Matrix3()
        //     );

        //     // 绕X轴旋转-俯仰
        //     // pitch += 0.01;
        //     // rot = Cesium.Matrix3.multiply(
        //     //  rotation,
        //     //  Cesium.Matrix3.fromRotationX(pitch),
        //     //  new Cesium.Matrix3()
        //     // );

        //     // 绕Y轴旋转-翻滚
        //     // roll += 0.01;
        //     // rot = Cesium.Matrix3.multiply(
        //     //     rotation,
        //     //     Cesium.Matrix3.fromRotationY(roll),
        //     //     new Cesium.Matrix3()
        //     // );
            
        //     orientation = Cesium.Quaternion.fromRotationMatrix(rot);
        //     // console.log("orientation",orientation)
        //     Frustum.update(origin, orientation);
        // }, 200)
    },

    // 创建视点
    createPoint(p) {
        return this.viewer.entities.add({
            position: p,
            point: {
                pixelSize: 10,
                color: new Cesium.Color(1.0, 1.0, 0.0, 1.0),
            },
        });
    },

    // 创建坐标系
    createFrame(p) {
        // X轴：红色，Y轴：绿色，Z轴：蓝色
        let modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(p);
        this.viewer.scene.primitives.add(
            new Cesium.DebugModelMatrixPrimitive({
                modelMatrix: modelMatrix,
                length: 500.0,
                width: 3.0,
            })
        );
    },
  }
}
