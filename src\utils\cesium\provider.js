export function getProviderViewModels() {
  // TODO
  // const tiandiKey = "4371a7bcd330fbad27256da9e9625526"; //天地图key，官网申请
  const tiandiKey = "78ba791c2e1f35e243e75eb025f86568"; //天地图key，官网申请
  let baseUrl = "http://t{s}.tianditu.com";
  let ishttps = "https:" == document.location.protocol ? true : false;
  if (ishttps) {
    baseUrl = "https://t{s}.tianditu.gov.cn";
  } else {
    baseUrl = "http://t{s}.tianditu.com";
  }
  //天地图矢量
  let tiandiVec = new Cesium.UrlTemplateImageryProvider({
    subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
    url: baseUrl + "/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=" + tiandiKey,
  });
  //天地图影像
  let tiandiImg = new Cesium.UrlTemplateImageryProvider({
    subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
    url: baseUrl + "/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=" + tiandiKey,
  });
  //天地图矢量标注
  let tiandiCva = new Cesium.UrlTemplateImageryProvider({
    subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
    url: baseUrl + "/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=" + tiandiKey,
  });

  //天地图影像标注
  let tiandiCia = new Cesium.UrlTemplateImageryProvider({
    subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
    url: baseUrl + "/DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=" + tiandiKey,
  });

  let tiandiVecModel = new Cesium.ProviderViewModel({
    name: "天地图",
    category: "国内地图资源",
    iconUrl: Cesium.buildModuleUrl(
      "./Widgets/Images/ImageryProviders/openStreetMap.png"
    ),
    tooltip: "WMTS 地图服务",
    creationFunction: function () {
      return [tiandiVec, tiandiCva];
    },
  });
  let tiandiImgModel = new Cesium.ProviderViewModel({
    name: "天地图影像",
    category: "国内地图资源",
    iconUrl: Cesium.buildModuleUrl(
      "./Widgets/Images/ImageryProviders/esriWorldImagery.png"
    ),
    tooltip: "WMTS 影像服务",
    creationFunction: function () {
      return [tiandiImg, tiandiCia];
    },
  });

  return [tiandiVecModel, tiandiImgModel];
}
