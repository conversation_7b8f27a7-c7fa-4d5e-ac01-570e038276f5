<template>
    <div :class="className" :style="{height:height,width:width}">
        <div class="w-height">
            <h3>无人机机型统计</h3>
            <img @click="tiao('/command/uva')" src="@/assets/images/index/godtl.png" >
        </div>
        <div class="w-box">
            <div ref="droneType" style="width:100%;height:100%;"></div>
        </div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts';
  import { indexDroneTypeStatistics } from '@/api/index/index'
  import resize from '../mixins/resize'
  export default {
    mixins: [resize],
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '350px'
      }
    },
    data() {
      return {
        x: [],
        y: [],
        loading: false,
        chart: null,

      }
    },
    mounted() {
        this.getData()
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        tiao(url){
            this.$router.push(url)
        },
        getData(){
            let params = {}
            this.loading = true
            indexDroneTypeStatistics(params).then(res => {
                console.log("无人机机型统计",res)
                this.tableData = res.data
                Object.keys(this.tableData).forEach(key => {
                    this.x.push(key)
                    this.y.push(this.tableData[key])
                })
                this.$nextTick(() => {
                    this.initChart()
                })
                this.loading = false
            }).catch(e => {
                this.loading = false
            })
        },
        initChart() {
            this.chart = echarts.init(this.$refs.droneType)
            this.chart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                    }
                },
                grid: {
                    top: 10,
                    left: '2%',
                    right: '2%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: this.x,
                    axisTick: {
                        alignWithLabel: true
                    },
                    axisLabel: {
                        fontSize: 12,
                        interval: 0,
                        formatter: function (params) {
                        var newParamsName = "";
                        var paramsNameNumber = params.length;
                        var provideNumber = 6;
                        // if(x.length < 11) {
                        //   provideNumber = 5
                        // }
                        var rowNumber = Math.ceil(paramsNameNumber / provideNumber);
                        if (paramsNameNumber > provideNumber) {
                            for (var p = 0; p < rowNumber; p++) {
                            var tempStr = "";
                            var start = p * provideNumber;
                            var end = start + provideNumber;
                            if (p == rowNumber - 1) {
                                tempStr = params.substring(start, paramsNameNumber);
                            } else {
                                tempStr = params.substring(start, end) + "\n";
                            }
                            newParamsName += tempStr;
                            }
                        } else {
                            newParamsName = params;
                        }
                        return newParamsName;
                        },
                    }
                }],
                yAxis: [{
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    minInterval: 1
                }],
                series: [
                    {
                        name: '数量',
                        type: 'bar',
                        barWidth: '60%',
                        data: this.y
                    }
                ]
            })
        }
    }
  }
  </script>

  <style lang="scss" scoped>
    .w-height {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(270deg, #BFD3F7 0%, #6694f0 100%);
        border-radius: 6px 6px 0px 0px;
        color: #FFFFFF;
        padding: 0 15px;
        h3 {
            font-size: 16px;
            font-weight: 700;
        }
        img {
            width: 25px;
        }
    }
    .w-box {
        width: 100%;
        height: calc(100% - 40px);
        padding: 10px 15px;
    }
  </style>
  