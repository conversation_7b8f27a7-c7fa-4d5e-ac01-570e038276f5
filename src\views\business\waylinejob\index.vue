<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务ID" prop="jobId">
        <el-input v-model="queryParams.jobId" placeholder="请输入任务ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入任务名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机场SN" prop="dockSn">
        <el-input v-model="queryParams.dockSn" placeholder="请输入机场SN" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['business:waylinejob:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:waylinejob:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:waylinejob:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:waylinejob:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>

    <el-table v-loading="loading" :data="waylinejobList">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="序号" align="center" prop="id" /> -->
      <el-table-column label="任务ID" align="center" prop="jobId" />
      <el-table-column label="任务名称" align="center" prop="name" />
      <!-- <el-table-column label="关联文件" align="center" prop="fileId" /> -->
      <el-table-column label="机场SN" align="center" prop="dockSn" />
      <!-- <el-table-column label="工作空间" align="center" prop="workspaceId" /> -->
      <el-table-column label="任务类型" align="center" prop="taskType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.wayline_job_type" :value="scope.row.taskType" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="航线类型" align="center" prop="waylineType" /> -->
      <!-- <el-table-column label="任务执行时间" align="center" prop="executeTime" />
      <el-table-column label="任务结束时间" align="center" prop="completedTime" /> -->
      <el-table-column label="任务执行时间" align="center" prop="executeTime">
        <template #default="scope">
          {{ parseTime(scope.row.executeTime) }}
        </template>
      </el-table-column>
      <el-table-column label="任务结束时间" align="center" prop="completedTime">
        <template #default="scope">
          {{ parseTime(scope.row.completedTime) }}
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="username" />
      <!-- <el-table-column label="计划开始时间" align="center" prop="beginTime" /> -->
      <!-- <el-table-column label="计划结束时间" align="center" prop="endTime" /> -->
      <el-table-column label="计划开始时间" align="center" prop="beginTime">
        <template #default="scope">
          {{ parseTime(scope.row.beginTime) }}
        </template>
      </el-table-column>
      <el-table-column label="计划结束时间" align="center" prop="endTime">
        <template #default="scope">
          {{ parseTime(scope.row.endTime) }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="任务状态" align="center" prop="status" /> -->
      <!-- <el-table-column label="任务状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.wayline_job_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="错误码" align="center" prop="errorCode" /> -->

      <el-table-column label="任务状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.errorMsg" placement="top" :disabled="scope.row.status === 3">
            <div style="display: inline-flex; align-items: center">
              <dict-tag :options="dict.type.wayline_job_status" :value="scope.row.status" />
              <i v-if="scope.row.status !== 3" class="el-icon-warning-outline"
                style="margin-left: 4px; color: #f56c6c;"></i>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column label="错误码" align="center" prop="errorCode" /> -->
      <el-table-column label="返航高度(m)" align="center" prop="rthAltitude" />
      <!-- <el-table-column label="失控执行" align="center" prop="outOfControl" /> -->
      <el-table-column label="失控执行" align="center" prop="outOfControl">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.wayline_job_loss_control" :value="scope.row.outOfControl" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="媒体文件数量" align="center" prop="mediaCount" /> -->

      <el-table-column label="媒体文件数量" align="center">
        <template #default="{ row }">
          <div class="upload-status">
            <span v-if="row.mediaCount === 0" class="status-dot yellow"></span>
            <span v-else-if="row.uploadedCount"
              :class="['status-dot', row.mediaCount === row.uploadedCount ? 'green' : 'blue']"></span>
            <span class="status-text">
              {{ row.mediaCount === 0 ?
                '没有媒体文件' :
                row.mediaCount === row.uploadedCount ?
                  `已上传(${row.uploadedCount}/${row.mediaCount})` :
                  `待上传(${row.uploadedCount}/${row.mediaCount})` }}
            </span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="" align="center" prop="createTime" />
      <el-table-column label="" align="center" prop="updateTime" />
      <el-table-column label="" align="center" prop="parentId" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 1 || scope.row.status === 2" size="mini" type="text"
            icon="el-icon-close" @click="handleCancelTask(scope.row)" style="color: #f56c6c;">取消任务</el-button>

          <el-button size="mini" type="text" icon="el-icon-video-camera" @click="handleRealTimeScene(scope.row)"
            style="color: #409EFF;">实时场景</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 弹窗对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <!-- 弹窗内容为空白 -->
      <div style="height: 200px;">
        <!-- 这里可以根据需要添加内容 -->
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 实时场景弹窗 -->
    <el-dialog title="实时场景" :visible.sync="rtmpDialogVisible" width="900px" append-to-body @close="handleCloseRtmp">
      <div id="rtmp-video-container">
        <video id="rtmpVideo" width="800" height="600" class="video-js vjs-default-skin" controls>
          <source :src="rtmpUrl" />
        </video>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handlePlayRtmp" type="primary">播放</el-button>
        <el-button @click="handleStopRtmp">停止</el-button>
        <el-button @click="rtmpDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.upload-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.green {
  background-color: #67C23A;
}

.blue {
  background-color: #409EFF;
}

.yellow {
  background-color: #E6A23C;
}

.status-text {
  font-size: 14px;
}
</style>

<script>
import { listWaylinejob, getWaylinejob, delWaylinejob, addWaylinejob, updateWaylinejob, cancelTask } from "@/api/business/waylinejob";

export default {
  name: "Waylinejob",
  dicts: ['wayline_job_type', 'wayline_job_status', 'wayline_job_loss_control'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // Wayline mission information of the dock.表格数据
      waylinejobList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobId: null,
        name: null,
        fileId: null,
        dockSn: null,
        workspaceId: null,
        taskType: null,
        waylineType: null,
        executeTime: null,
        completedTime: null,
        username: null,
        beginTime: null,
        endTime: null,
        errorCode: null,
        status: null,
        rthAltitude: null,
        outOfControl: null,
        mediaCount: null,
        createTime: null,
        updateTime: null,
        parentId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        jobId: [
          { required: true, message: "任务ID不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
        fileId: [
          { required: true, message: "关联文件不能为空", trigger: "blur" }
        ],
        dockSn: [
          { required: true, message: "机场SN不能为空", trigger: "blur" }
        ],
        workspaceId: [
          { required: true, message: "工作空间不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "change" }
        ],
        waylineType: [
          { required: true, message: "航线类型不能为空", trigger: "change" }
        ],
        username: [
          { required: true, message: "创建人不能为空", trigger: "blur" }
        ],
        beginTime: [
          { required: true, message: "计划开始时间不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "任务状态1: pending; 2: in progress; 3: success; 4: cancel; 5: failed不能为空", trigger: "change" }
        ],
        rthAltitude: [
          { required: true, message: "返航高度 min: 20m; max: 500m不能为空", trigger: "blur" }
        ],
        outOfControl: [
          { required: true, message: "失控执行 0: go home; 1: hover; 2: landing;不能为空", trigger: "blur" }
        ],
        mediaCount: [
          { required: true, message: "媒体文件数量不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
      },
      // 实时场景相关
      rtmpDialogVisible: false,
      rtmpPlayer: null,
      rtmpUrl: '',
      currentTask: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询Wayline mission information of the dock.列表 */
    getList() {
      this.loading = true;
      listWaylinejob(this.queryParams).then(response => {
        this.waylinejobList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        jobId: null,
        name: null,
        fileId: null,
        dockSn: null,
        workspaceId: null,
        taskType: null,
        waylineType: null,
        executeTime: null,
        completedTime: null,
        username: null,
        beginTime: null,
        endTime: null,
        errorCode: null,
        status: null,
        rthAltitude: null,
        outOfControl: null,
        mediaCount: null,
        createTime: null,
        updateTime: null,
        parentId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加Wayline mission information of the dock.";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWaylinejob(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改Wayline mission information of the dock.";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWaylinejob(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWaylinejob(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除Wayline mission information of the dock.编号为"' + ids + '"的数据项？').then(function () {
        return delWaylinejob(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/waylinejob/export', {
        ...this.queryParams
      }, `waylinejob_${new Date().getTime()}.xlsx`)
    },
    /** 取消任务操作 */
    handleCancelTask(row) {
      this.$modal.confirm(`是否确认取消任务"${row.name}"？`).then(() => {
        return cancelTask(row.jobId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("任务取消成功");
      }).catch(() => { });
    },
    /** 处理实时场景按钮 */
    handleRealTimeScene(row) {
      this.currentTask = row;
      // 根据任务信息构建RTMP流地址
      // this.rtmpUrl = `rtmp://192.168.1.99:1935/live/${row.dockSn}-${row.jobId}`;
      this.rtmpUrl = `http://172.16.5.70:8080/live/livestream.flv`;


      this.rtmpDialogVisible = true;
    },
    /** 播放RTMP流 */
    handlePlayRtmp() {
      if (this.rtmpPlayer) {
        this.rtmpPlayer.dispose();
      }
      this.$nextTick(() => {
        this.rtmpPlayer = videojs("rtmpVideo", {
          fluid: true,
          responsive: true,
          techOrder: ['flvjs', 'html5'],
          flvjs: {
            mediaDataSource: {
              isLive: false,
              cors: true,
              withCredentials: false
            }
          },
          sources: [{
            type: "video/x-flv"
          }]
        }, () => {
          console.log("RTMP播放器初始化成功");
        });

         this.rtmpPlayer.play().catch(err => {
        console.error("播放失败:", err);
        this.$modal.msgError("播放失败，请检查网络连接");
      });
      })


     
    },

    /** 停止RTMP流 */
    handleStopRtmp() {
      if (this.rtmpPlayer) {
        this.rtmpPlayer.pause();
      }
    },

    /** 关闭RTMP弹窗 */
    handleCloseRtmp() {
      if (this.rtmpPlayer) {
        this.rtmpPlayer.dispose();
        this.rtmpPlayer = null;
      }

      // 重新初始化video元素
      this.$nextTick(() => {
        const container = document.getElementById("rtmp-video-container");
        if (container) {
          container.innerHTML = `
            <video
              id="rtmpVideo"
              width="800"
              height="600"
              class="video-js vjs-default-skin"
              controls
            >
              <source src="${this.rtmpUrl}" />
            </video>
          `;
        }
      });
    },
  },
  beforeDestroy() {
    if (this.rtmpPlayer) {
      this.rtmpPlayer.dispose();
    }
  }
};
</script>
