<template>
    <div class="app-container">
      <div class="w-container">
        <div class="w-title">任务编辑</div>
        <div class="w-from">
          <div class="w-left">
            <el-form ref="form1" label-position="top" label-width="80px" :model="fromData" :rules="rules" @submit.native.prevent>
              <el-form-item label="任务名称" prop="taskName">
                  <el-input v-model="fromData.taskName" placeholder="请输入任务名称"></el-input>
              </el-form-item>
              <el-form-item label="任务描述">
                  <el-input type="textarea" maxlength="200" show-word-limit :rows="3" v-model="fromData.taskDesc" placeholder="请输入任务描述"></el-input>
              </el-form-item>
              <el-form-item label="任务模式" prop="taskType">
                <el-radio-group v-model="fromData.taskType">
                  <el-radio :label="1">立即执行</el-radio>
                  <el-radio :label="2">定时模式</el-radio>
                  <el-radio :label="3">循环模式</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-form ref="form2" label-position="left" :model="fromData" :rules="rules" v-if="fromData.taskType == 2">
              <el-form-item label="执行时间：" prop="taskEstimatedTime">
                <el-date-picker
                  v-model="fromData.taskEstimatedTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :picker-options="DisableDatesOption"
                  placeholder="请选择日期时间">
                </el-date-picker>
              </el-form-item>
            </el-form>
            <el-form ref="form3" label-position="left" :model="fromData" :rules="rules" v-if="fromData.taskType == 3">
              <el-form-item label="日期范围：" prop="taskValidList">
                <el-date-picker
                  v-model="fromData.taskValidList"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="执行时间：" v-for="(item,index) in fromData.taskTimeList" :key="index">
                <el-time-picker
                  v-model="fromData.taskTimeList[index]"
                  value-format="HHmmss"
                  placeholder="请选择时间">
                </el-time-picker>
                <el-button v-if="index == 0" style="margin-left:10px;" icon="el-icon-plus" @click="addTaskTime"></el-button>
                <el-button v-else style="margin-left:10px;" icon="el-icon-minus" @click="removeTaskTime(index)"></el-button>
              </el-form-item>
              <el-form-item label="循环日期：" prop="taskCycleMode">
                <el-radio-group v-model="fromData.taskCycleMode">
                  <el-radio label="DAY">按天循环</el-radio>
                  <el-radio label="WEEK">按周循环</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="每" prop="taskInterval" v-if="fromData.taskCycleMode == 'DAY'">
                <el-input-number size="small" :min="1" :max="7" v-model="fromData.taskInterval"></el-input-number>&nbsp;天
              </el-form-item>
              <el-form-item label="周" prop="taskInterval" v-if="fromData.taskCycleMode == 'WEEK'">
                <el-select v-model="fromData.weekList" multiple clearable placeholder="请选择">
                  <el-option label="周一" value="2"> </el-option>
                  <el-option label="周二" value="3"> </el-option>
                  <el-option label="周三" value="4"> </el-option>
                  <el-option label="周四" value="5"> </el-option>
                  <el-option label="周五" value="6"> </el-option>
                  <el-option label="周六" value="7"> </el-option>
                  <el-option label="周天" value="1"> </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="w-right">
            <el-form ref="form4" label-position="top" label-width="80px" @submit.native.prevent>
              <el-form-item label="关联航线">
                  <el-input v-model="routesName" clearable placeholder="请输入航线名称搜索" @keyup.enter.native="getList"></el-input>
              </el-form-item>
            </el-form>
            <el-table :data="routesList" style="width: 100%;" height="calc(100% - 104px)" border>
              <el-table-column width="50" align="center"> 
                <template slot-scope="scope">
                  <el-radio v-model="fromData.wayPointId" :label="scope.row.routesId">{{""}}</el-radio>
                </template>
              </el-table-column>
              <el-table-column prop="routesName" align="center" label="航线名称" show-overflow-tooltip width="150"> </el-table-column>
              <el-table-column prop="routesStartPoint" align="center" label="起点" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="routesEndPoint" align="center" label="终点" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="createTime" align="center" label="创建时间" show-overflow-tooltip width="180"> </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="w-btn">
          <el-button size="small" @click="toback">取消</el-button>
          <el-button size="small" @click="submitSave" type="primary">确认</el-button>
        </div>
      </div>
    </div>
  </template>
      
<script>
import { routesList } from "@/api/cruise/airline";
import { addTask, TaskDet, editTask } from "@/api/cruise/task";
  export default {
    name: "addtask",
    data() {
      return {
        // 遮罩层
        loading: false,
        fromData: {
          id: null,
          taskCycleMode: "DAY", // 循环模式.DAY=按天循环,WEEK=按周循环
          taskDesc: "", // 描述
          taskEstimatedTime: "",//定时执行模式下的定时时间,yyyy-MM-dd hh:mm:ss
          taskInterval: 1, //执行间隔.按天循环表示每隔几天,数字类型.周循环时表示每周几(1=周日,2=周一类推),逗号间隔
          taskName: "", // 任务名称
          taskTime: "", // 执行时间 hh:mm:ss
          taskType: 1, //任务执行模式.1=立即执行,2=定时执行,3=循环模式
          taskValidTime: "", //任务生效时间,yyyy-MM-dd
          taskInvalidTime: "",//任务失效时间,yyyy-MM-dd
          wayPointId: null, // 关联航线ID
          taskValidList: [], // 日期区间
          taskTimeList: [null], // 多个执行时间
          weekList: [], 
        },
        routesName: "",
        routesList: [],
        rules: {
          taskName: [
            { required: true, message: "任务名称不能为空", trigger: "blur" }
          ],
          taskType: [
            { required: true, message: "任务模式不能为空", trigger: "blur" }
          ],
          taskEstimatedTime: [
            { required: true, message: "请选择执行时间", trigger: "change" }
          ],
          taskValidList: [
            { required: true, message: "请选择日期范围", trigger: "change" }
          ],
          taskInterval: [
            { required: true, message: "请选择间隔时间", trigger: "blur" }
          ],
          wayPointId: [
          { required: true, message: "请选择航线", trigger: "blur" }
          ]
        },
        DisableDatesOption:{
          disabledDate(date) {
            return date.getTime() < Date.now()
          }
        },
      };
    },
    created() {
      this.getList()
      console.log(this.$route.params.id)
      if(this.$route.params.id != 0) {
        this.getDet(this.$route.params.id)
      }
    },
    methods: {
      getDet(id){
        TaskDet(id).then(res => {
          console.log("任务详情",res)
          this.fromData = res.data
          if(this.fromData.taskValidTime && this.fromData.taskInvalidTime) {
            this.fromData.taskValidList = [this.fromData.taskValidTime, this.fromData.taskInvalidTime]
          } else {
            this.fromData.taskValidList = []
          }

          if(this.fromData.taskTime) {
            this.fromData.taskTimeList = this.fromData.taskTime.split(",")
          } else {
            this.fromData.taskTimeList = [null]
          }

          if(this.fromData.taskCycleMode == "WEEK") {
            this.fromData.weekList = this.fromData.taskInterval.split(",")
          } else {
            this.fromData.weekList = []
          }
        })
      },
      addTaskTime(){
        this.fromData.taskTimeList.push(null)
      },
      removeTaskTime(index){
        this.fromData.taskTimeList.splice(index,1)
      },
      getList() {
        let params = {
          pageNum:1,
          pageSize: 999,
          routesName: this.routesName
        }
        routesList(params).then((response) => {
          console.log("航线列表", response);
          this.routesList = response.rows;
        });
      },
      toback(){
        this.$router.go(-1)
      },
      async submitSave(){
        // console.log("this.fromData",this.fromData)
        if(this.fromData.taskType == 1) {
          let valid = await this.$refs["form1"].validate()
          if(!this.fromData.wayPointId) {
            this.$modal.msgError("请选择航线");
            return ;
          }
          if(valid) {
            if(this.fromData.id) {
              editTask(this.fromData).then(res => {
                console.log("提交成功",res)
                this.$modal.msgSuccess("保存成功");
                this.toback()
              })
            } else {
              addTask(this.fromData).then(res => {
                console.log("提交成功",res)
                this.$modal.msgSuccess("保存成功");
                this.toback()
              })
            }
          }
        } else if(this.fromData.taskType == 2) {
          let valid1 = await this.$refs["form1"].validate()
          let valid2 = await this.$refs["form2"].validate()
          if(!this.fromData.wayPointId) {
            this.$modal.msgError("请选择航线");
            return ;
          }
          if(valid1 && valid2) {
            if(this.fromData.id) {
              editTask(this.fromData).then(res => {
                console.log("提交成功",res)
                this.$modal.msgSuccess("保存成功");
                this.toback()
              })
            } else {
              addTask(this.fromData).then(res => {
                console.log("提交成功",res)
                this.$modal.msgSuccess("保存成功");
                this.toback()
              })
            }
          }
        } else {
          let valid1 = await this.$refs["form1"].validate()
          let valid3 = await this.$refs["form3"].validate()
          if(!this.fromData.wayPointId) {
            this.$modal.msgError("请选择航线");
            return ;
          }
          if(valid1 && valid3) {
            if(this.fromData.taskValidList.length == 2) {
              this.fromData.taskValidTime = this.fromData.taskValidList[0]
              this.fromData.taskInvalidTime = this.fromData.taskValidList[1]
            }
            let timeList = []
            this.fromData.taskTimeList.forEach(item => {
              if(item) {
                timeList.push(item)
              }
            })
            if(timeList.length > 0) {
              this.fromData.taskTime = timeList.join(',')
            } else {
              this.$modal.msgError("请选择执行时间");
              return ;
            }
            if(this.fromData.taskCycleMode == "WEEK") {
              if(this.fromData.weekList.length == 0) {
                this.$modal.msgError("请选择周");
              } else {
                this.fromData.taskInterval = this.fromData.weekList.join(',')
              }
            }
            if(this.fromData.id) {
              editTask(this.fromData).then(res => {
                console.log("提交成功",res)
                this.$modal.msgSuccess("保存成功");
                this.toback()
              })
            } else {
              addTask(this.fromData).then(res => {
                console.log("提交成功",res)
                this.$modal.msgSuccess("保存成功");
                this.toback()
              })
            }
          }
        }
      }
    }
  };
</script>
      
  <style lang="scss" scoped>
  .app-container {
    background: #f5f5f5;
  }

  .w-container {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
  }

  .w-title {
    width: 100%;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
    padding: 0 20px;
    box-shadow: 0 5px 15px #0000000d;
    color: #606266;
    font-weight: 700;
  }
  .w-from {
    width: 100%;
    height: calc(100% - 110px);
    padding: 20px 20px 0 20px;
    display: flex;
    .w-left {
      width: 50%;
      height: 100%;
      padding-right: 20px;
      border-right: 1px solid #DCDFE6 ;
      overflow: auto;
      .w-time-select {
        width: 100%;
        height: 200px;
        background: #000;
      }
    }
    .w-right {
      width: 50%;
      height: 100%;
      padding-left: 20px;
      overflow: auto;
    }
  }
  .w-btn {
    width: 100%;
    height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 20px;
  }
  </style>