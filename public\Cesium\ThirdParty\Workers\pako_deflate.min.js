!function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).pako=t()}((function(){return function t(e,a,n){function r(s,h){if(!a[s]){if(!e[s]){var l="function"==typeof require&&require;if(!h&&l)return l(s,!0);if(i)return i(s,!0);var o=new Error("Cannot find module '"+s+"'");throw o.code="MODULE_NOT_FOUND",o}var _=a[s]={exports:{}};e[s][0].call(_.exports,(function(t){return r(e[s][1][t]||t)}),_,_.exports,t,e,a,n)}return a[s].exports}for(var i="function"==typeof require&&require,s=0;s<n.length;s++)r(n[s]);return r}({1:[function(t,e,a){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;a.assign=function(t){for(var e,a,n=Array.prototype.slice.call(arguments,1);n.length;){var r=n.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var i in r)e=r,a=i,Object.prototype.hasOwnProperty.call(e,a)&&(t[i]=r[i])}}return t},a.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var r={arraySet:function(t,e,a,n,r){if(e.subarray&&t.subarray)t.set(e.subarray(a,a+n),r);else for(var i=0;i<n;i++)t[r+i]=e[a+i]},flattenChunks:function(t){var e,a,n,r,i,s;for(e=n=0,a=t.length;e<a;e++)n+=t[e].length;for(s=new Uint8Array(n),e=r=0,a=t.length;e<a;e++)i=t[e],s.set(i,r),r+=i.length;return s}},i={arraySet:function(t,e,a,n,r){for(var i=0;i<n;i++)t[r+i]=e[a+i]},flattenChunks:function(t){return[].concat.apply([],t)}};a.setTyped=function(t){t?(a.Buf8=Uint8Array,a.Buf16=Uint16Array,a.Buf32=Int32Array,a.assign(a,r)):(a.Buf8=Array,a.Buf16=Array,a.Buf32=Array,a.assign(a,i))},a.setTyped(n)},{}],2:[function(t,e,a){"use strict";var n=t("./common"),r=!0,i=!0;try{String.fromCharCode.apply(null,[0])}catch(t){r=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){i=!1}for(var s=new n.Buf8(256),h=0;h<256;h++)s[h]=252<=h?6:248<=h?5:240<=h?4:224<=h?3:192<=h?2:1;function l(t,e){if(e<65534&&(t.subarray&&i||!t.subarray&&r))return String.fromCharCode.apply(null,n.shrinkBuf(t,e));for(var a="",s=0;s<e;s++)a+=String.fromCharCode(t[s]);return a}s[254]=s[254]=1,a.string2buf=function(t){var e,a,r,i,s,h=t.length,l=0;for(i=0;i<h;i++)55296==(64512&(a=t.charCodeAt(i)))&&i+1<h&&56320==(64512&(r=t.charCodeAt(i+1)))&&(a=65536+(a-55296<<10)+(r-56320),i++),l+=a<128?1:a<2048?2:a<65536?3:4;for(e=new n.Buf8(l),i=s=0;s<l;i++)55296==(64512&(a=t.charCodeAt(i)))&&i+1<h&&56320==(64512&(r=t.charCodeAt(i+1)))&&(a=65536+(a-55296<<10)+(r-56320),i++),a<128?e[s++]=a:(a<2048?e[s++]=192|a>>>6:(a<65536?e[s++]=224|a>>>12:(e[s++]=240|a>>>18,e[s++]=128|a>>>12&63),e[s++]=128|a>>>6&63),e[s++]=128|63&a);return e},a.buf2binstring=function(t){return l(t,t.length)},a.binstring2buf=function(t){for(var e=new n.Buf8(t.length),a=0,r=e.length;a<r;a++)e[a]=t.charCodeAt(a);return e},a.buf2string=function(t,e){var a,n,r,i,h=e||t.length,o=new Array(2*h);for(a=n=0;a<h;)if((r=t[a++])<128)o[n++]=r;else if(4<(i=s[r]))o[n++]=65533,a+=i-1;else{for(r&=2===i?31:3===i?15:7;1<i&&a<h;)r=r<<6|63&t[a++],i--;1<i?o[n++]=65533:r<65536?o[n++]=r:(r-=65536,o[n++]=55296|r>>10&1023,o[n++]=56320|1023&r)}return l(o,n)},a.utf8border=function(t,e){var a;for((e=e||t.length)>t.length&&(e=t.length),a=e-1;0<=a&&128==(192&t[a]);)a--;return a<0||0===a?e:a+s[t[a]]>e?a:e}},{"./common":1}],3:[function(t,e,a){"use strict";e.exports=function(t,e,a,n){for(var r=65535&t|0,i=t>>>16&65535|0,s=0;0!==a;){for(a-=s=2e3<a?2e3:a;i=i+(r=r+e[n++]|0)|0,--s;);r%=65521,i%=65521}return r|i<<16|0}},{}],4:[function(t,e,a){"use strict";var n=function(){for(var t,e=[],a=0;a<256;a++){t=a;for(var n=0;n<8;n++)t=1&t?3988292384^t>>>1:t>>>1;e[a]=t}return e}();e.exports=function(t,e,a,r){var i=n,s=r+a;t^=-1;for(var h=r;h<s;h++)t=t>>>8^i[255&(t^e[h])];return-1^t}},{}],5:[function(t,e,a){"use strict";var n,r=t("../utils/common"),i=t("./trees"),s=t("./adler32"),h=t("./crc32"),l=t("./messages"),o=-2,_=258,d=262,u=113;function f(t,e){return t.msg=l[e],e}function c(t){return(t<<1)-(4<t?9:0)}function p(t){for(var e=t.length;0<=--e;)t[e]=0}function g(t){var e=t.state,a=e.pending;a>t.avail_out&&(a=t.avail_out),0!==a&&(r.arraySet(t.output,e.pending_buf,e.pending_out,a,t.next_out),t.next_out+=a,e.pending_out+=a,t.total_out+=a,t.avail_out-=a,e.pending-=a,0===e.pending&&(e.pending_out=0))}function m(t,e){i._tr_flush_block(t,0<=t.block_start?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,g(t.strm)}function b(t,e){t.pending_buf[t.pending++]=e}function v(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function w(t,e){var a,n,r=t.max_chain_length,i=t.strstart,s=t.prev_length,h=t.nice_match,l=t.strstart>t.w_size-d?t.strstart-(t.w_size-d):0,o=t.window,u=t.w_mask,f=t.prev,c=t.strstart+_,p=o[i+s-1],g=o[i+s];t.prev_length>=t.good_match&&(r>>=2),h>t.lookahead&&(h=t.lookahead);do{if(o[(a=e)+s]===g&&o[a+s-1]===p&&o[a]===o[i]&&o[++a]===o[i+1]){i+=2,a++;do{}while(o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&o[++i]===o[++a]&&i<c);if(n=_-(c-i),i=c-_,s<n){if(t.match_start=e,h<=(s=n))break;p=o[i+s-1],g=o[i+s]}}}while((e=f[e&u])>l&&0!=--r);return s<=t.lookahead?s:t.lookahead}function y(t){var e,a,n,i,l,o,_,u,f,c,p=t.w_size;do{if(i=t.window_size-t.lookahead-t.strstart,t.strstart>=p+(p-d)){for(r.arraySet(t.window,t.window,p,p,0),t.match_start-=p,t.strstart-=p,t.block_start-=p,e=a=t.hash_size;n=t.head[--e],t.head[e]=p<=n?n-p:0,--a;);for(e=a=p;n=t.prev[--e],t.prev[e]=p<=n?n-p:0,--a;);i+=p}if(0===t.strm.avail_in)break;if(o=t.strm,_=t.window,u=t.strstart+t.lookahead,c=void 0,(f=i)<(c=o.avail_in)&&(c=f),a=0===c?0:(o.avail_in-=c,r.arraySet(_,o.input,o.next_in,c,u),1===o.state.wrap?o.adler=s(o.adler,_,c,u):2===o.state.wrap&&(o.adler=h(o.adler,_,c,u)),o.next_in+=c,o.total_in+=c,c),t.lookahead+=a,t.lookahead+t.insert>=3)for(l=t.strstart-t.insert,t.ins_h=t.window[l],t.ins_h=(t.ins_h<<t.hash_shift^t.window[l+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[l+3-1])&t.hash_mask,t.prev[l&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=l,l++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<d&&0!==t.strm.avail_in)}function k(t,e){for(var a,n;;){if(t.lookahead<d){if(y(t),t.lookahead<d&&0===e)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==a&&t.strstart-a<=t.w_size-d&&(t.match_length=w(t,a)),t.match_length>=3)if(n=i._tr_tally(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){for(t.match_length--;t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart,0!=--t.match_length;);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else n=i._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(n&&(m(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,4===e?(m(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(m(t,!1),0===t.strm.avail_out)?1:2}function z(t,e){for(var a,n,r;;){if(t.lookahead<d){if(y(t),t.lookahead<d&&0===e)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==a&&t.prev_length<t.max_lazy_match&&t.strstart-a<=t.w_size-d&&(t.match_length=w(t,a),t.match_length<=5&&(1===t.strategy||3===t.match_length&&4096<t.strstart-t.match_start)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){for(r=t.strstart+t.lookahead-3,n=i._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;++t.strstart<=r&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!=--t.prev_length;);if(t.match_available=0,t.match_length=2,t.strstart++,n&&(m(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((n=i._tr_tally(t,0,t.window[t.strstart-1]))&&m(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(n=i._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,4===e?(m(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(m(t,!1),0===t.strm.avail_out)?1:2}function x(t,e,a,n,r){this.good_length=t,this.max_lazy=e,this.nice_length=a,this.max_chain=n,this.func=r}function B(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new r.Buf16(1146),this.dyn_dtree=new r.Buf16(122),this.bl_tree=new r.Buf16(78),p(this.dyn_ltree),p(this.dyn_dtree),p(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new r.Buf16(16),this.heap=new r.Buf16(573),p(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new r.Buf16(573),p(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function A(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=2,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?42:u,t.adler=2===e.wrap?0:1,e.last_flush=0,i._tr_init(e),0):f(t,o)}function C(t){var e,a=A(t);return 0===a&&((e=t.state).window_size=2*e.w_size,p(e.head),e.max_lazy_match=n[e.level].max_lazy,e.good_match=n[e.level].good_length,e.nice_match=n[e.level].nice_length,e.max_chain_length=n[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),a}function S(t,e,a,n,i,s){if(!t)return o;var h=1;if(-1===e&&(e=6),n<0?(h=0,n=-n):15<n&&(h=2,n-=16),i<1||9<i||8!==a||n<8||15<n||e<0||9<e||s<0||4<s)return f(t,o);8===n&&(n=9);var l=new B;return(t.state=l).strm=t,l.wrap=h,l.gzhead=null,l.w_bits=n,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=i+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+3-1)/3),l.window=new r.Buf8(2*l.w_size),l.head=new r.Buf16(l.hash_size),l.prev=new r.Buf16(l.w_size),l.lit_bufsize=1<<i+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new r.Buf8(l.pending_buf_size),l.d_buf=1*l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=e,l.strategy=s,l.method=a,C(t)}n=[new x(0,0,0,0,(function(t,e){var a=65535;for(a>t.pending_buf_size-5&&(a=t.pending_buf_size-5);;){if(t.lookahead<=1){if(y(t),0===t.lookahead&&0===e)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var n=t.block_start+a;if((0===t.strstart||t.strstart>=n)&&(t.lookahead=t.strstart-n,t.strstart=n,m(t,!1),0===t.strm.avail_out))return 1;if(t.strstart-t.block_start>=t.w_size-d&&(m(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(m(t,!0),0===t.strm.avail_out?3:4):(t.strstart>t.block_start&&(m(t,!1),t.strm.avail_out),1)})),new x(4,4,8,4,k),new x(4,5,16,8,k),new x(4,6,32,32,k),new x(4,4,16,16,z),new x(8,16,32,32,z),new x(8,16,128,128,z),new x(8,32,128,256,z),new x(32,128,258,1024,z),new x(32,258,258,4096,z)],a.deflateInit=function(t,e){return S(t,e,8,15,8,0)},a.deflateInit2=S,a.deflateReset=C,a.deflateResetKeep=A,a.deflateSetHeader=function(t,e){return t&&t.state?2!==t.state.wrap?o:(t.state.gzhead=e,0):o},a.deflate=function(t,e){var a,r,s,l;if(!t||!t.state||5<e||e<0)return t?f(t,o):o;if(r=t.state,!t.output||!t.input&&0!==t.avail_in||666===r.status&&4!==e)return f(t,0===t.avail_out?-5:o);if(r.strm=t,a=r.last_flush,r.last_flush=e,42===r.status)if(2===r.wrap)t.adler=0,b(r,31),b(r,139),b(r,8),r.gzhead?(b(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),b(r,255&r.gzhead.time),b(r,r.gzhead.time>>8&255),b(r,r.gzhead.time>>16&255),b(r,r.gzhead.time>>24&255),b(r,9===r.level?2:2<=r.strategy||r.level<2?4:0),b(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(b(r,255&r.gzhead.extra.length),b(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(t.adler=h(t.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(b(r,0),b(r,0),b(r,0),b(r,0),b(r,0),b(r,9===r.level?2:2<=r.strategy||r.level<2?4:0),b(r,3),r.status=u);else{var d=8+(r.w_bits-8<<4)<<8;d|=(2<=r.strategy||r.level<2?0:r.level<6?1:6===r.level?2:3)<<6,0!==r.strstart&&(d|=32),d+=31-d%31,r.status=u,v(r,d),0!==r.strstart&&(v(r,t.adler>>>16),v(r,65535&t.adler)),t.adler=1}if(69===r.status)if(r.gzhead.extra){for(s=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>s&&(t.adler=h(t.adler,r.pending_buf,r.pending-s,s)),g(t),s=r.pending,r.pending!==r.pending_buf_size));)b(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>s&&(t.adler=h(t.adler,r.pending_buf,r.pending-s,s)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(73===r.status)if(r.gzhead.name){s=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>s&&(t.adler=h(t.adler,r.pending_buf,r.pending-s,s)),g(t),s=r.pending,r.pending===r.pending_buf_size)){l=1;break}b(r,l=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0)}while(0!==l);r.gzhead.hcrc&&r.pending>s&&(t.adler=h(t.adler,r.pending_buf,r.pending-s,s)),0===l&&(r.gzindex=0,r.status=91)}else r.status=91;if(91===r.status)if(r.gzhead.comment){s=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>s&&(t.adler=h(t.adler,r.pending_buf,r.pending-s,s)),g(t),s=r.pending,r.pending===r.pending_buf_size)){l=1;break}b(r,l=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0)}while(0!==l);r.gzhead.hcrc&&r.pending>s&&(t.adler=h(t.adler,r.pending_buf,r.pending-s,s)),0===l&&(r.status=103)}else r.status=103;if(103===r.status&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&g(t),r.pending+2<=r.pending_buf_size&&(b(r,255&t.adler),b(r,t.adler>>8&255),t.adler=0,r.status=u)):r.status=u),0!==r.pending){if(g(t),0===t.avail_out)return r.last_flush=-1,0}else if(0===t.avail_in&&c(e)<=c(a)&&4!==e)return f(t,-5);if(666===r.status&&0!==t.avail_in)return f(t,-5);if(0!==t.avail_in||0!==r.lookahead||0!==e&&666!==r.status){var w=2===r.strategy?function(t,e){for(var a;;){if(0===t.lookahead&&(y(t),0===t.lookahead)){if(0===e)return 1;break}if(t.match_length=0,a=i._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,a&&(m(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(m(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(m(t,!1),0===t.strm.avail_out)?1:2}(r,e):3===r.strategy?function(t,e){for(var a,n,r,s,h=t.window;;){if(t.lookahead<=_){if(y(t),t.lookahead<=_&&0===e)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&0<t.strstart&&(n=h[r=t.strstart-1])===h[++r]&&n===h[++r]&&n===h[++r]){s=t.strstart+_;do{}while(n===h[++r]&&n===h[++r]&&n===h[++r]&&n===h[++r]&&n===h[++r]&&n===h[++r]&&n===h[++r]&&n===h[++r]&&r<s);t.match_length=_-(s-r),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(a=i._tr_tally(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(a=i._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),a&&(m(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(m(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(m(t,!1),0===t.strm.avail_out)?1:2}(r,e):n[r.level].func(r,e);if(3!==w&&4!==w||(r.status=666),1===w||3===w)return 0===t.avail_out&&(r.last_flush=-1),0;if(2===w&&(1===e?i._tr_align(r):5!==e&&(i._tr_stored_block(r,0,0,!1),3===e&&(p(r.head),0===r.lookahead&&(r.strstart=0,r.block_start=0,r.insert=0))),g(t),0===t.avail_out))return r.last_flush=-1,0}return 4!==e?0:r.wrap<=0?1:(2===r.wrap?(b(r,255&t.adler),b(r,t.adler>>8&255),b(r,t.adler>>16&255),b(r,t.adler>>24&255),b(r,255&t.total_in),b(r,t.total_in>>8&255),b(r,t.total_in>>16&255),b(r,t.total_in>>24&255)):(v(r,t.adler>>>16),v(r,65535&t.adler)),g(t),0<r.wrap&&(r.wrap=-r.wrap),0!==r.pending?0:1)},a.deflateEnd=function(t){var e;return t&&t.state?42!==(e=t.state.status)&&69!==e&&73!==e&&91!==e&&103!==e&&e!==u&&666!==e?f(t,o):(t.state=null,e===u?f(t,-3):0):o},a.deflateSetDictionary=function(t,e){var a,n,i,h,l,_,d,u,f=e.length;if(!t||!t.state)return o;if(2===(h=(a=t.state).wrap)||1===h&&42!==a.status||a.lookahead)return o;for(1===h&&(t.adler=s(t.adler,e,f,0)),a.wrap=0,f>=a.w_size&&(0===h&&(p(a.head),a.strstart=0,a.block_start=0,a.insert=0),u=new r.Buf8(a.w_size),r.arraySet(u,e,f-a.w_size,a.w_size,0),e=u,f=a.w_size),l=t.avail_in,_=t.next_in,d=t.input,t.avail_in=f,t.next_in=0,t.input=e,y(a);a.lookahead>=3;){for(n=a.strstart,i=a.lookahead-2;a.ins_h=(a.ins_h<<a.hash_shift^a.window[n+3-1])&a.hash_mask,a.prev[n&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=n,n++,--i;);a.strstart=n,a.lookahead=2,y(a)}return a.strstart+=a.lookahead,a.block_start=a.strstart,a.insert=a.lookahead,a.lookahead=0,a.match_length=a.prev_length=2,a.match_available=0,t.next_in=_,t.input=d,t.avail_in=l,a.wrap=h,0},a.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":1,"./adler32":3,"./crc32":4,"./messages":6,"./trees":7}],6:[function(t,e,a){"use strict";e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],7:[function(t,e,a){"use strict";var n=t("../utils/common");function r(t){for(var e=t.length;0<=--e;)t[e]=0}var i=256,s=286,h=30,l=15,o=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],_=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],u=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],f=new Array(576);r(f);var c=new Array(60);r(c);var p=new Array(512);r(p);var g=new Array(256);r(g);var m=new Array(29);r(m);var b,v,w,y=new Array(h);function k(t,e,a,n,r){this.static_tree=t,this.extra_bits=e,this.extra_base=a,this.elems=n,this.max_length=r,this.has_stree=t&&t.length}function z(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function x(t){return t<256?p[t]:p[256+(t>>>7)]}function B(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function A(t,e,a){t.bi_valid>16-a?(t.bi_buf|=e<<t.bi_valid&65535,B(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=a-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=a)}function C(t,e,a){A(t,a[2*e],a[2*e+1])}function S(t,e){for(var a=0;a|=1&t,t>>>=1,a<<=1,0<--e;);return a>>>1}function j(t,e,a){var n,r,i=new Array(16),s=0;for(n=1;n<=l;n++)i[n]=s=s+a[n-1]<<1;for(r=0;r<=e;r++){var h=t[2*r+1];0!==h&&(t[2*r]=S(i[h]++,h))}}function E(t){var e;for(e=0;e<s;e++)t.dyn_ltree[2*e]=0;for(e=0;e<h;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function U(t){8<t.bi_valid?B(t,t.bi_buf):0<t.bi_valid&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function D(t,e,a,n){var r=2*e,i=2*a;return t[r]<t[i]||t[r]===t[i]&&n[e]<=n[a]}function I(t,e,a){for(var n=t.heap[a],r=a<<1;r<=t.heap_len&&(r<t.heap_len&&D(e,t.heap[r+1],t.heap[r],t.depth)&&r++,!D(e,n,t.heap[r],t.depth));)t.heap[a]=t.heap[r],a=r,r<<=1;t.heap[a]=n}function O(t,e,a){var n,r,s,h,l=0;if(0!==t.last_lit)for(;n=t.pending_buf[t.d_buf+2*l]<<8|t.pending_buf[t.d_buf+2*l+1],r=t.pending_buf[t.l_buf+l],l++,0===n?C(t,r,e):(C(t,(s=g[r])+i+1,e),0!==(h=o[s])&&A(t,r-=m[s],h),C(t,s=x(--n),a),0!==(h=_[s])&&A(t,n-=y[s],h)),l<t.last_lit;);C(t,256,e)}function q(t,e){var a,n,r,i=e.dyn_tree,s=e.stat_desc.static_tree,h=e.stat_desc.has_stree,o=e.stat_desc.elems,_=-1;for(t.heap_len=0,t.heap_max=573,a=0;a<o;a++)0!==i[2*a]?(t.heap[++t.heap_len]=_=a,t.depth[a]=0):i[2*a+1]=0;for(;t.heap_len<2;)i[2*(r=t.heap[++t.heap_len]=_<2?++_:0)]=1,t.depth[r]=0,t.opt_len--,h&&(t.static_len-=s[2*r+1]);for(e.max_code=_,a=t.heap_len>>1;1<=a;a--)I(t,i,a);for(r=o;a=t.heap[1],t.heap[1]=t.heap[t.heap_len--],I(t,i,1),n=t.heap[1],t.heap[--t.heap_max]=a,t.heap[--t.heap_max]=n,i[2*r]=i[2*a]+i[2*n],t.depth[r]=(t.depth[a]>=t.depth[n]?t.depth[a]:t.depth[n])+1,i[2*a+1]=i[2*n+1]=r,t.heap[1]=r++,I(t,i,1),2<=t.heap_len;);t.heap[--t.heap_max]=t.heap[1],function(t,e){var a,n,r,i,s,h,o=e.dyn_tree,_=e.max_code,d=e.stat_desc.static_tree,u=e.stat_desc.has_stree,f=e.stat_desc.extra_bits,c=e.stat_desc.extra_base,p=e.stat_desc.max_length,g=0;for(i=0;i<=l;i++)t.bl_count[i]=0;for(o[2*t.heap[t.heap_max]+1]=0,a=t.heap_max+1;a<573;a++)p<(i=o[2*o[2*(n=t.heap[a])+1]+1]+1)&&(i=p,g++),o[2*n+1]=i,_<n||(t.bl_count[i]++,s=0,c<=n&&(s=f[n-c]),h=o[2*n],t.opt_len+=h*(i+s),u&&(t.static_len+=h*(d[2*n+1]+s)));if(0!==g){do{for(i=p-1;0===t.bl_count[i];)i--;t.bl_count[i]--,t.bl_count[i+1]+=2,t.bl_count[p]--,g-=2}while(0<g);for(i=p;0!==i;i--)for(n=t.bl_count[i];0!==n;)_<(r=t.heap[--a])||(o[2*r+1]!==i&&(t.opt_len+=(i-o[2*r+1])*o[2*r],o[2*r+1]=i),n--)}}(t,e),j(i,_,t.bl_count)}function T(t,e,a){var n,r,i=-1,s=e[1],h=0,l=7,o=4;for(0===s&&(l=138,o=3),e[2*(a+1)+1]=65535,n=0;n<=a;n++)r=s,s=e[2*(n+1)+1],++h<l&&r===s||(h<o?t.bl_tree[2*r]+=h:0!==r?(r!==i&&t.bl_tree[2*r]++,t.bl_tree[32]++):h<=10?t.bl_tree[34]++:t.bl_tree[36]++,i=r,(h=0)===s?(l=138,o=3):r===s?(l=6,o=3):(l=7,o=4))}function L(t,e,a){var n,r,i=-1,s=e[1],h=0,l=7,o=4;for(0===s&&(l=138,o=3),n=0;n<=a;n++)if(r=s,s=e[2*(n+1)+1],!(++h<l&&r===s)){if(h<o)for(;C(t,r,t.bl_tree),0!=--h;);else 0!==r?(r!==i&&(C(t,r,t.bl_tree),h--),C(t,16,t.bl_tree),A(t,h-3,2)):h<=10?(C(t,17,t.bl_tree),A(t,h-3,3)):(C(t,18,t.bl_tree),A(t,h-11,7));i=r,(h=0)===s?(l=138,o=3):r===s?(l=6,o=3):(l=7,o=4)}}r(y);var N=!1;function R(t,e,a,r){var i,s,h;A(t,0+(r?1:0),3),s=e,h=a,U(i=t),B(i,h),B(i,~h),n.arraySet(i.pending_buf,i.window,s,h,i.pending),i.pending+=h}a._tr_init=function(t){N||(function(){var t,e,a,n,r,i=new Array(16);for(n=a=0;n<28;n++)for(m[n]=a,t=0;t<1<<o[n];t++)g[a++]=n;for(g[a-1]=n,n=r=0;n<16;n++)for(y[n]=r,t=0;t<1<<_[n];t++)p[r++]=n;for(r>>=7;n<h;n++)for(y[n]=r<<7,t=0;t<1<<_[n]-7;t++)p[256+r++]=n;for(e=0;e<=l;e++)i[e]=0;for(t=0;t<=143;)f[2*t+1]=8,t++,i[8]++;for(;t<=255;)f[2*t+1]=9,t++,i[9]++;for(;t<=279;)f[2*t+1]=7,t++,i[7]++;for(;t<=287;)f[2*t+1]=8,t++,i[8]++;for(j(f,287,i),t=0;t<h;t++)c[2*t+1]=5,c[2*t]=S(t,5);b=new k(f,o,257,s,l),v=new k(c,_,0,h,l),w=new k(new Array(0),d,0,19,7)}(),N=!0),t.l_desc=new z(t.dyn_ltree,b),t.d_desc=new z(t.dyn_dtree,v),t.bl_desc=new z(t.bl_tree,w),t.bi_buf=0,t.bi_valid=0,E(t)},a._tr_stored_block=R,a._tr_flush_block=function(t,e,a,n){var r,s,h=0;0<t.level?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,a=4093624447;for(e=0;e<=31;e++,a>>>=1)if(1&a&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<i;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),q(t,t.l_desc),q(t,t.d_desc),h=function(t){var e;for(T(t,t.dyn_ltree,t.l_desc.max_code),T(t,t.dyn_dtree,t.d_desc.max_code),q(t,t.bl_desc),e=18;3<=e&&0===t.bl_tree[2*u[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),r=t.opt_len+3+7>>>3,(s=t.static_len+3+7>>>3)<=r&&(r=s)):r=s=a+5,a+4<=r&&-1!==e?R(t,e,a,n):4===t.strategy||s===r?(A(t,2+(n?1:0),3),O(t,f,c)):(A(t,4+(n?1:0),3),function(t,e,a,n){var r;for(A(t,e-257,5),A(t,a-1,5),A(t,n-4,4),r=0;r<n;r++)A(t,t.bl_tree[2*u[r]+1],3);L(t,t.dyn_ltree,e-1),L(t,t.dyn_dtree,a-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,h+1),O(t,t.dyn_ltree,t.dyn_dtree)),E(t),n&&U(t)},a._tr_tally=function(t,e,a){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&a,t.last_lit++,0===e?t.dyn_ltree[2*a]++:(t.matches++,e--,t.dyn_ltree[2*(g[a]+i+1)]++,t.dyn_dtree[2*x(e)]++),t.last_lit===t.lit_bufsize-1},a._tr_align=function(t){var e;A(t,2,3),C(t,256,f),16===(e=t).bi_valid?(B(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):8<=e.bi_valid&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}},{"../utils/common":1}],8:[function(t,e,a){"use strict";e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],"/lib/deflate.js":[function(t,e,a){"use strict";var n=t("./zlib/deflate"),r=t("./utils/common"),i=t("./utils/strings"),s=t("./zlib/messages"),h=t("./zlib/zstream"),l=Object.prototype.toString;function o(t){if(!(this instanceof o))return new o(t);this.options=r.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},t||{});var e=this.options;e.raw&&0<e.windowBits?e.windowBits=-e.windowBits:e.gzip&&0<e.windowBits&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new h,this.strm.avail_out=0;var a=n.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(0!==a)throw new Error(s[a]);if(e.header&&n.deflateSetHeader(this.strm,e.header),e.dictionary){var _;if(_="string"==typeof e.dictionary?i.string2buf(e.dictionary):"[object ArrayBuffer]"===l.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,0!==(a=n.deflateSetDictionary(this.strm,_)))throw new Error(s[a]);this._dict_set=!0}}function _(t,e){var a=new o(e);if(a.push(t,!0),a.err)throw a.msg||s[a.err];return a.result}o.prototype.push=function(t,e){var a,s,h=this.strm,o=this.options.chunkSize;if(this.ended)return!1;s=e===~~e?e:!0===e?4:0,"string"==typeof t?h.input=i.string2buf(t):"[object ArrayBuffer]"===l.call(t)?h.input=new Uint8Array(t):h.input=t,h.next_in=0,h.avail_in=h.input.length;do{if(0===h.avail_out&&(h.output=new r.Buf8(o),h.next_out=0,h.avail_out=o),1!==(a=n.deflate(h,s))&&0!==a)return this.onEnd(a),!(this.ended=!0);0!==h.avail_out&&(0!==h.avail_in||4!==s&&2!==s)||("string"===this.options.to?this.onData(i.buf2binstring(r.shrinkBuf(h.output,h.next_out))):this.onData(r.shrinkBuf(h.output,h.next_out)))}while((0<h.avail_in||0===h.avail_out)&&1!==a);return 4===s?(a=n.deflateEnd(this.strm),this.onEnd(a),this.ended=!0,0===a):2!==s||(this.onEnd(0),!(h.avail_out=0))},o.prototype.onData=function(t){this.chunks.push(t)},o.prototype.onEnd=function(t){0===t&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=r.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},a.Deflate=o,a.deflate=_,a.deflateRaw=function(t,e){return(e=e||{}).raw=!0,_(t,e)},a.gzip=function(t,e){return(e=e||{}).gzip=!0,_(t,e)}},{"./utils/common":1,"./utils/strings":2,"./zlib/deflate":5,"./zlib/messages":6,"./zlib/zstream":8}]},{},[])("/lib/deflate.js")}));