<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="航线名称" prop="routesName">
        <el-input
          v-model="queryParams.routesName"
          placeholder="请输入航线名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd(0)"
          v-hasPermi="['notroutes:info:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleImport()"
          v-hasPermi="['notroutes:info:add']"
          >导入</el-button
        >
      </el-form-item>
    </el-form>

    <div class="w-table" v-loading="loading">
      <el-empty description="暂无数据" v-if="total == 0"></el-empty>

      <el-row :gutter="20" v-else>
        <el-col :span="12" v-for="item in tableList" :key="item.routesId">
          <div class="w-box" @click="handleAdd(item.routesId)">
            <div class="w-left">
              <img :src="item.routesPreviewUrl" />
            </div>
            <div class="w-right">
              <p class="w-title">{{ item.routesName }}</p>
              <!-- <el-tooltip
                class="item"
                effect="dark"
                :disabled="item.routesStartPoint > 16"
                :content="item.routesStartPoint"
                placement="top-start"
              >
                <p>起点：{{ item.routesStartPoint }}</p>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                :disabled="item.routesEndPoint > 16"
                :content="item.routesEndPoint"
                placement="top-start"
              >
                <p>终点：{{ item.routesEndPoint }}</p>
              </el-tooltip> -->
              <p>创建时间：{{ item.createTime }}</p>
              <i
                v-hasPermi="['notroutes:info:remove']"
                class="el-icon-delete w-delete"
                @click.stop="handleDelete(item)"
              ></i>
              <i
                v-hasPermi="['notroutes:info:list']"
                class="el-icon-download w-download"
                @click.stop="downloadFile(item)"
              ></i>
              <!-- <i class="el-icon-share w-share" @click.stop="share(item)"></i> -->
            </div>
          </div>
        </el-col>
      </el-row>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <el-dialog
      title="新建航线"
      :visible.sync="openAdd"
      width="500px"
      append-to-body
    >
      <el-form
        ref="addform"
        :model="addform"
        :rules="addrules"
        label-width="80px"
      >
        <el-form-item label="航线名称" prop="routesName">
          <el-input v-model="addform.routesName" placeholder="请输入航线名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="导入航线"
      :visible.sync="openImport"
      width="500px"
      append-to-body  @open="importSubmitDisabled = false"
    >
      <el-form
        ref="importform"
        :model="importform"
        :rules="importrules"
        label-width="80px"
      >
        <el-form-item label="航线名称" prop="routesName">
          <el-input v-model="importform.routesName" placeholder="请输入航线名称" />
        </el-form-item>
        <el-form-item label="航线文件" prop="file">
          <el-upload
            ref="upload"
            :action="uploadFileUrl"
            :limit="1"
            accept=".kmz,.kml"
            :on-change="handleFileChange"
            :auto-upload="false"
            :file-list="fileList"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传kmz/kml格式文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="importSubmitDisabled" @click="importSubmit">确 定</el-button>
        <el-button @click="openImport = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="下发飞手"
      :visible.sync="openshare"
      width="630px"
      append-to-body
    >
      <el-transfer
        filterable
        v-model="selectUser"
        :props="{
          key: 'user_id',
          label: 'nick_name'
        }"
        :data="allUser">
      </el-transfer>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveShare">确 定</el-button>
        <el-button @click="openshare = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
import { notAirportList, DeleteNotAirportRoutes, getFlyByRoutesId, updateUserRoutes, uploadKmzWithRoute } from "@/api/cruise/airline";
export default {
  name: "Dict",
  data() {
    return {
      // 遮罩层
      loading: false,
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/business/kmz/uploadKmz", // 上传文件服务器地址
      // 总条数
      total: 0,
      // 预案表格数据
      tableList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        routesName: undefined,
      },
      addform: {
        routesName: undefined,
        airportId: undefined,
      },
      addrules: {
        routesName: [
          { required: true, message: "请输入航线名称", trigger: "blur" }
        ]
      },
      openImport: false,
      importform: {
        routesName: "",
        file: null
      },
      importrules: {
        routesName: [
          { required: true, message: "请输入航线名称", trigger: "blur" }
        ],
        file: [
          { required: true, message: "请上传航线文件", trigger: "change" }
        ]
      },
      importSubmitDisabled: false,
      openAdd: false,
      airportList: [],
      openshare: false,
      selectUser: [],
      allUser: [],
      routesId: null,
      fileList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    saveShare(){
      let params = {
        routesId: this.routesId,
        userIds: this.selectUser.join(',')
      }
      updateUserRoutes(params).then(res => {
        console.log("确定",res)
        this.$modal.msgSuccess("下发成功！")
        this.openshare = false
      })
    },
    share(data){
      console.log(data)
      this.selectUser = []
      getFlyByRoutesId(data.routesId).then(res => {
        console.log("分享列表", res)
        this.routesId = data.routesId
        this.allUser = res.data.allUser
        this.selectUser = res.data.routesUser
        this.openshare = true
      })
    },
    handleFileChange(file) {
      this.importform.file = file.raw;
    },
    importSubmit() {
      this.importSubmitDisabled = true;  // 点击立即禁用按钮
      this.$refs["importform"].validate(valid => {
        if (valid) {
          uploadKmzWithRoute({
            routesName: this.importform.routesName,
            file: this.importform.file
          }).then(response => {
            if (response.code === 200) {
              this.$message.success("导入成功");
              this.openImport = false;
              // this.$emit("refresh");

              this.resetQuery();
            } else {
              this.$message.error(response.msg);
            }
          }).catch(error => {
            this.$message.error("上传失败：" + error.message);
          });
        }
      })
    },
    downloadFile(data){
      const elt = document.createElement('a');
      elt.setAttribute('href', data.routesMioioPath);
      elt.setAttribute('download', + data.routesName + '.kmz');
      elt.style.display = 'none';
      document.body.appendChild(elt);
      elt.click();
      document.body.removeChild(elt);
    },
    reset() {
      this.addform = {
        routesName: undefined,
      };
      this.resetForm("addform");
    },
    submitForm() {
        this.$refs["addform"].validate(valid => {
            if(valid) {
                this.$router.push({
                    path: "/command/uvaLine-edit/edit/0",
                    query: {
                        routesName: this.addform.routesName
                    }
                })
                // this.$router.push({
                //     path: "/command/uvaLine-edit/g_edit/0",
                //     query: {
                //         routesName: this.addform.routesName
                //     }
                // })
            }
        })
    },
    cancel() {
        this.openAdd = false
        this.reset()
    },
    /** 查询预案列表 */
    getList() {
      this.loading = true;
      notAirportList(
        this.addDateRange(this.queryParams, this.dateRange, "CreateTime")
      ).then((response) => {
        console.log("航线", response);
        this.tableList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(id) {
        if(id == 0) {
            this.openAdd = true
        } else {
            this.$router.push("/command/uvaLine-edit/edit/" + id)
        }
    },
     /** 新增按钮操作 */
     handleImport() {
        this.openImport = true;
        this.resetForm("importform");
        this.fileList = []
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除航线名称为"' + row.routesName + '"的数据项？')
        .then(function () {
          return DeleteNotAirportRoutes(row.routesId);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
  
  <style lang="scss" scoped>
.w-table {
  width: 100%;
  min-height: calc(100% - 51px);
}
.w-box {
  height: 160px;
  position: relative;
  cursor: pointer;
  border-radius: 5px;
  overflow: hidden;

  .w-left {
    position: absolute;
    width: 250px;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 999;
    overflow: hidden;
    img {
      height: 100%;
      transform: scale(2);
      transform-origin: center;
    }
  }
  .w-right {
    position: absolute;
    width: calc(100% - 250px);
    height: 100%;
    right: 0;
    top: 0;
    background: #f7f7f7;
    padding: 10px 20px;
    overflow: hidden;
    p {
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      font-size: 13px;
      margin: 15px 0;
    }
    .w-title {
      font-size: 14px;
      margin: 10px 0;
    }
    .w-delete {
      position: absolute;
      bottom: 20px;
      right: 20px;
      color: #999;
    }
    .w-delete:hover {
      color: #1890ff;
    }
    .w-download {
      position: absolute;
      bottom: 20px;
      right: 50px;
      color: #999;
    }
    .w-download:hover {
      color: #1890ff;
    }
    .w-share {
      position: absolute;
      bottom: 20px;
      right: 75px;
      color: #999;
    }
    .w-share:hover {
      color: #1890ff;
    }
  }
}

::v-deep {
  .el-col {
    margin-bottom: 20px;
  }
}
</style>