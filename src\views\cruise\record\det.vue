<template>
    <div class="app-container">
        <div class="w-map" id="w-record-task"></div>
        <div class="w-right">
            <p>起飞时间：{{recordDataDet.taskBeginTime}}</p>
            <p>完成时间：{{recordDataDet.taskEndTime}}</p>
            <p>任务总耗时：{{recordDataDet.taskTimeStr}}</p>
            <p>任务总里程：{{recordDataDet.routeMileage}}米</p>
            <p>执行机场：{{recordDataDet.airportName}}</p>
            <p>执行无人机：{{recordDataDet.airportChildName}}</p>
            <p>机场地点：{{recordDataDet.airportAddress}}</p>
            <div class="w-right-btn1">
                <el-button style="float:left;" size="medium" type="primary" @click="startAnimation" >回放轨迹</el-button>
                <el-button style="float:right;" size="medium" type="primary" @click="pauseAnimation">暂停回放</el-button>
            </div>
            <div class="w-right-btn2">
                <el-button style="float:left;" size="medium" type="primary" @click="resumeAnimation">继续回放</el-button>
                <el-button style="float:right;" size="medium" type="primary" @click="stopAnimation">停止回放</el-button>
            </div>
        </div>
        <div class="w-bottom">
            <div class="w-toleft" @click="toleft"><i class="el-icon-arrow-left"></i></div>
            <div class="w-box" ref="sliderFather">
                <div :style="{left:left + 'px'}" ref="slider">
                    <div v-for="item in mediaList" :key="item.id">
                        <el-image :preview-src-list="imgList" style="height: 160px;margin-right: 10px;width: 280px;" :src="item.mediaUrl" alt="" v-if="item.mediaType == 1"></el-image>
                        <video :src="item.mediaUrl" v-else controls></video>
                    </div>
                </div>
            </div>
            <div class="w-toright" @click="toRight"><i class="el-icon-arrow-right"></i></div>
        </div>
    </div>
</template>

<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import { amapKey, amapSecretkey } from "@/utils/use-g-map";
import { recordTaskDet, recordTaskOsdDet, recordTaskMediaDet } from "@/api/cruise/monitor";
export default {
    name: "record_det",
    data() {
        return {
            map: null, // 地图实例
            zoom: 12,
            osdList: [],
            recordDataDet: {},
            marker:null,
            lineArr: [],
            flightDistance: 0,
            imgList: [],
            mediaList: [],
            left: 0
        };    
    },
    created() {
        this.getRecordMediaDet()
        this.getRecordDet()
    },
    mounted() {
        this.initMap()
    },
    methods: {
        toleft(){
            if((this.left + 290) < 0) {
                this.left += 290
            } else {
                this.left = 0
            }
        },
        toRight(){
            let sliderFather = this.$refs.sliderFather
            let slider = this.$refs.slider
            // console.log("Math.abs(this.left)",Math.abs(this.left))
            // console.log("(slider.offsetWidth - sliderFather)",(slider.offsetWidth - sliderFather.offsetWidth))
            if(Math.abs(this.left) < (slider.offsetWidth - sliderFather.offsetWidth)) {
                this.left = this.left - 290
            } else {
            }
        },
        startAnimation(){
            if(this.marker) {
                this.marker.moveAlong(this.lineArr, {
                    // 每一段的时长
                    duration: 1000,//可根据实际采集时间间隔设置
                    // JSAPI2.0 是否延道路自动设置角度在 moveAlong 里设置
                    autoRotation: true,
                });
            }
        },
        pauseAnimation(){
            if(this.marker) {
                this.marker.pauseMove();
            }
        },
        resumeAnimation(){
            if(this.marker) {
                this.marker.resumeMove();
            }
        },
        stopAnimation(){
            if(this.marker) {
                this.marker.stopMove();
            }
        },
        getRecordOsdDet(){
            let _this = this
            let params = {
                recordId:this.$route.params.id,
                pageNum: 1,
                pageSize: 9999,
            }
            recordTaskOsdDet(params).then(res => {
                console.log("坐标信息",res)
                _this.osdList = res.rows || []
                _this.lineArr =  _this.osdList.map(item => {
                    return [item.longitude,item.latitude];
                })
                if(_this.lineArr.length > 0) {
                    _this.map.setCenter(_this.lineArr[0])
                    const icon = new AMap.Icon({
                        size: new AMap.Size(30, 30),
                        image: require("@/assets/images/uva.png"),
                        imageSize: new AMap.Size(30, 30),
                        imageOffset: new AMap.Pixel(0, 0),
                    });
                    _this.marker = new AMap.Marker({
                        map: _this.map,
                        position: _this.lineArr[0],
                        icon: icon,
                        offset: new AMap.Pixel(-15, -15),
                    });

                    var polyline = new AMap.Polyline({
                        map: _this.map,
                        path: _this.lineArr,
                        showDir:true,
                        strokeColor: "#28F",  //线颜色
                        // strokeOpacity: 1,     //线透明度
                        strokeWeight: 6,      //线宽
                        // strokeStyle: "solid"  //线样式
                    });
                    _this.flightDistance = polyline.getLength()
                    var passedPolyline = new AMap.Polyline({
                        map: _this.map,
                        strokeColor: "#AF5",  //线颜色
                        strokeWeight: 6,      //线宽
                    });

                    _this.marker.on('moving', function (e) {
                        passedPolyline.setPath(e.passedPath);
                        _this.map.setCenter(e.target.getPosition())
                    });
                    _this.map.setFitView();
                }
            })
        },
        getRecordMediaDet(){
            let params = {
                recordId:this.$route.params.id,
                pageNum: 1,
                pageSize: 9999,
            }
            recordTaskMediaDet(params).then(res => {
                console.log("媒体信息",res)
                this.mediaList = res.rows
                this.mediaList.forEach(item => {
                    if(item.mediaType == 1) {
                        this.imgList.push(item.mediaUrl)
                    }
                })
            })
        },
        getRecordDet(){
            recordTaskDet(this.$route.params.id).then(res => {
                console.log("详情",res)
                this.recordDataDet = res.data
            })
        },
        //初始化地图
        initMap() {
            let _this = this;
            window._AMapSecurityConfig = {
                securityJsCode: amapSecretkey
            }
            AMapLoader.load({
                key: amapKey,
                version: "2.0",
                plugins: [
                    "AMap.MoveAnimation"
                ],
            })
            .then((AMap) => {
                _this.map = new AMap.Map("w-record-task", {
                    zoom: _this.zoom,
                    // mapStyle: "amap://styles/whitesmoke",
                    WebGLParams: {preserveDrawingBuffer: true}
                });

                _this.getRecordOsdDet()
            })
            .catch((e) => {
                console.log(e);
            });
        },
    }
};
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0;
    background: #f5f5f5;
}
.w-map {
    width: calc(100% - 330px);
    height: calc(100% - 250px);
    border-radius: 10px;
    border: 1px solid rgba(73, 68, 78, 0.2);
    overflow: hidden;
    position: absolute;
    top: 20px;
    left: 20px;
}
.w-right {
    width: 280px;
    height: calc(100% - 250px);
    position: absolute;
    top: 20px;
    right: 20px;
    background: #4b4b51df;
    border-radius: 10px;
    color: #fff;
    padding: 20px;
    font-size: 14px;
    overflow: hidden;
    .w-right-btn1 {
        position: absolute;
        width: 100%;
        height: 30px;
        bottom: 70px;
        left: 0;
        padding: 0 20px;
    }
    .w-right-btn2 {
        position: absolute;
        width: 100%;
        height: 30px;
        bottom: 20px;
        left: 0;
        padding: 0 20px;
    }
}
.w-bottom {
    width: calc(100% - 40px);
    height: 200px;
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: #4b4b51df;
    border-radius: 10px;
    .w-toleft {
        width: 30px;
        height: calc(100% - 80px);
        background: #3e3e429f;
        position: absolute;
        left: 20px;
        bottom: 40px;
        border-radius: 10px 0 0 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        cursor: pointer;
        font-size: 20px;
    }
    .w-toleft:hover {
        background: #3e3e42c4;
    }
    .w-toright {
        width: 30px;
        height: calc(100% - 80px);
        background: #3e3e429f;
        position: absolute;
        right: 20px;
        bottom: 40px;
        border-radius: 0 10px 10px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        cursor: pointer;
        font-size: 20px;
    }
    .w-toright:hover {
        background: #3e3e42c4;
    }
    .w-box {
        width: calc(100% - 120px);
        height: 160px;
        position: absolute;
        top: 20px;
        left: 60px;
        overflow: hidden;
        &>div {
            position: absolute;
            top: 0;
            // left: 0;
            width: auto;
            height: 160px;
            display: flex;
            align-items: center;
            transition: all .3s;
            &>div {
                width: auto;
                height: 160px;
                img {
                    height: 160px;
                    margin-right: 10px;
                }
                video {
                    width: 280px;
                    height: 160px;
                    margin-right: 10px;
                }
            }
        }
        
        
    }
}
</style>

