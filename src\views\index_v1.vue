<template>
  <div class="dashboard-editor-container">
    <!-- <el-row :gutter="20" v-if="indexType == 1 || indexType == 3">
      <el-col :span="8">
        <div class="chart-wrapper">
          <action-list />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-wrapper">
          <plan-state />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-wrapper">
          <drone-type />
        </div>
      </el-col>
      <el-col :span="24">
        <div class="chart-wrapper">
          <action-year />
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" v-if="indexType == 2 || indexType == 3">
      <el-col :span="8">
        <div class="chart-wrapper">
          <task-list />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-wrapper">
          
          <task-future />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart-wrapper">
          <air-room />
        </div>
      </el-col>
      <el-col :span="24">
        <div class="chart-wrapper">
          <record-year />
        </div>
      </el-col>
    </el-row> -->
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import ActionList from "./index/action/actionList.vue";
import ActionYear from "./index/action/actionYear.vue";
import DroneType from "./index/action/droneType.vue";

import AirRoom from "./index/airport/airRoom.vue";
import RecordYear from "./index/airport/recordYear.vue";
import TaskFuture from "./index/airport/taskFuture.vue";
import TaskList from "./index/airport/taskList.vue";

// import { encrypt, decrypt} from '@/utils/crypto'

export default {
  name: "Index",
  components: {
    ActionList,
    ActionYear,
    DroneType,
    AirRoom,
    RecordYear,
    TaskFuture,
    TaskList
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters(["indexType"]),
  },
  mounted() {
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 20px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    width: 100%;
    background: #fff;
    // padding: 15px;
    margin-bottom: 20px;
  }
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
