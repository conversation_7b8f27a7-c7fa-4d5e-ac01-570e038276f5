<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="行动状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="行动状态"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in dict.type.sys_moveabout_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="行动名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入行动名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button type="primary" plain icon="el-icon-plus" size="mini" v-hasPermi="['plan:action:edit']" @click="handleAdd(0)" >新增</el-button>
        </el-form-item>
      </el-form>
  
      <el-table v-loading="loading" :data="tableList">
        
        <el-table-column label="行动名称" align="center" prop="name" :show-overflow-tooltip="true" />
        <el-table-column label="行动简介" align="center" prop="introduction" :show-overflow-tooltip="true" />
        <el-table-column label="行动状态" align="center" prop="status" >
          <template slot-scope="scope">
            <span>{{scope.row.status == 1 ? "进行中" : "已结束"}}</span>
          </template>
        </el-table-column>
        <el-table-column label="行动人员" align="center" prop="pilotName" ></el-table-column>
        <el-table-column label="占用空间" align="center" prop="mediaSize" >
          <template slot-scope="scope">
            <span>{{scope.row.mediaSize | mediaSize}}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="createByName" width="150"></el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['plan:action:edit']"
              v-show="scope.row.status == 1"
            >编辑</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-switch-button"
              @click="handleFinish(scope.row)"
              v-hasPermi="['plan:action:end']"
              v-show="scope.row.status == 1"
            >结束</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['plan:action:remove']"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-tickets"
              @click="handleDet(scope.row)"
              v-hasPermi="['plan:record:query']"
              v-show="scope.row.status == 2"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

    </div>
  </template>
  
  <script>
  import { listAction, DeleteAction, endAction } from "@/api/command/moveabout";
  export default {
    name: "active",
    dicts: ['sys_moveabout_status'],
    data() {
      return {
        // 遮罩层
        loading: false,
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        // 日期范围
        dateRange: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          name: undefined,
          status: undefined
        },
      };
    },
    created() {
      this.getList();
    },
    methods: {
      // 行动记录
      handleDet(row){
        console.log(row)
        this.$router.push('/command/action-det/action/' + row.id)
      },
      
      // 结束行动
      handleFinish(row){
        let params = {
          id: row.id,
        }
        endAction(params).then(res => {
          this.$modal.msgSuccess("操作成功");
          this.getList()
        })
      },
      /** 行动列表 */
      getList() {
        this.loading = true;
        listAction(this.addDateRange(this.queryParams, this.dateRange, 'CreateTime')).then(response => {
          console.log("行动列表",response)
            this.tableList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      /** 新增按钮操作 */
      handleAdd(id) {
        this.$router.push("/command/action-edit/edit/"+ id);
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.$router.push("/command/action-edit/edit/"+ row.id);
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        this.$modal.confirm('是否确认删除行动名为"' + row.name + '"的数据项？').then(function() {
          return DeleteAction(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },
    },
    filters:{
      mediaSize(val){
        if(val) {
          if(val < 1024) {
            return val + 'b';
          } else if( val < 1024 * 1024) {
            return Math.round((val/1024) * 100) / 100 + 'Kb';
          } else if(val < 1024 * 1024 * 1024) {
            return Math.round((val/1024/1024) * 100) / 100 + 'Mb';
          } else {
            return Math.round((val/1024/1024/1024) * 100) / 100 + 'Gb';
          }
        } else {
          return val;
        }
      }
    }
  };
  </script>