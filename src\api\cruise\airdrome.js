import request from '@/utils/request'
import axios from 'axios';
import { amapServerKey } from "@/utils/use-g-map"


// 获取机场列表
export function listAirport(query) {
    return request({
      url: '/device/airport/volist',
      method: 'get',
      params: query
    })
}

// 根据经纬度获取地址信息
export function getRegeo(query) {
    return axios({
        url: "https://restapi.amap.com/v3/geocode/regeo",
        method: "get",
        params: {
            key: amapServerKey,
            ...query
        }
    })
}

// 根据区域获取天气信息
export function getWeatherInfo(query) {
    return axios({
        url: "https://restapi.amap.com/v3/weather/weatherInfo",
        method: "get",
        params: {
            key: amapServerKey,
            ...query
        }
    })
}

// 获取机场详情
export function detAirport(id) {
    return request({
    //   url: '/device/airport/getInfoList/' + id,
      url: '/device/airport/' + id,

      method: 'get'
    })
}

// 修改机场详情
export function putAirport(data) {
    return request({
      url: '/device/airport',
      method: 'put',
      data
    })
}
