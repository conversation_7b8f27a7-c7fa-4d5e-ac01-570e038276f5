export function parseTime(timestamp, pattern) {
    if (!timestamp) return null
    const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
    const date = new Date(timestamp.toString().length === 10 ? timestamp * 1000 : timestamp)
    
    const formatObj = {
      y: date.getFullYear(),
      m: date.getMonth() + 1,
      d: date.getDate(),
      h: date.getHours(),
      i: date.getMinutes(),
      s: date.getSeconds()
    }
  
    return format.replace(/{(y|m|d|h|i|s)+}/g, (result, key) => {
      let value = formatObj[key]
      if (result.length > 0 && value < 10) {
        value = '0' + value
      }
      return value || 0
    })
  }
  