<template>
    <div :class="className" :style="{height:height,width:width}">
        <div class="w-height">
            <h3>正在进行中的行动</h3>
            <img @click="tiao('/command/moveabout')" src="@/assets/images/index/godtl.png" >
        </div>
        <div class="w-box">
            <el-table v-loading="loading" :data="tableData" style="width:100%;" height="290">
                <el-table-column label="行动名称" align="center" prop="name" :show-overflow-tooltip="true" />
                <el-table-column label="行动简介" align="center" prop="introduction" :show-overflow-tooltip="true" />
                <!-- <el-table-column label="行动人员" align="center" prop="pilotName" :show-overflow-tooltip="true"></el-table-column> -->
                <el-table-column label="创建时间" align="center" prop="createTime" :show-overflow-tooltip="true"/>
            </el-table>
        </div>
    </div>
  </template>
  
  <script>
  import { indexActionStatistics } from '@/api/index/index'
  export default {
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '350px'
      }
    },
    data() {
      return {
        tableData: [],
        loading: false
      }
    },
    mounted() {
        this.getData()
    },
    methods: {
        tiao(url){
            this.$router.push(url)
        },
        getData(){
            let params = {
                pageNum: 1,
                pageSize: 10
            }
            this.loading = true
            indexActionStatistics(params).then(res => {
                console.log("正在执行的任务",res)
                this.tableData = res.rows || []
                this.loading = false
            }).catch(e => {
                this.loading = false
            })
        }
    }
  }
  </script>

  <style lang="scss" scoped>
    .w-height {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(270deg, #BFD3F7 0%, #6694f0 100%);
        border-radius: 6px 6px 0px 0px;
        color: #FFFFFF;
        padding: 0 15px;
        h3 {
            font-size: 16px;
            font-weight: 700;
        }
        img {
            width: 25px;
        }
    }
    .w-box {
        width: 100%;
        height: calc(100% - 40px);
        padding: 10px 15px;
    }
  </style>
  