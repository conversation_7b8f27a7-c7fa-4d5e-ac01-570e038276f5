import AMapLoader from "@amap/amap-jsapi-loader";
// export function initMap (container) {
//     AMapLoader.load({
//         key: 'ae8b61c4350c86ac95f9639b54934ccf',
//         version: '2.0',
//         plugins: [
//             'AMap.Scale',
//             'AMap.ToolBar',
//             'AMap.ControlBar',
//             'AMap.ElasticMarker',
//             'AMap.MapType',
//             'AMap.Geocoder',
//             'AMap.CircleEditor',
//             'AMap.PolygonEditor',
//             'AMap.PolylineEditor',
//             'AMap.PolyEditor',
//             'AMap.RangingTool',
//             'AMap.Weather',
//             'AMap.MouseTool',
//             'AMap.MoveAnimation'
//         ]
//     }).then((AMap) => {
//         let map = new AMap.Map(container, {
//             center: [113.935913, 22.525335],
//             zoom: 15
//         })
//     return map;
//     }).catch(e => {
//         console.log(e)
//     })
// }

//TODO 高德地图
// export const amapSecretkey = "02fc653232d5af3f1b64443969ca5bdd";
// export const amapKey = "ae8b61c4350c86ac95f9639b54934ccf";
export const amapSecretkey = "380ad6d3bc6d474c2a27a6b982b28e91";
export const amapKey = "441efee85047598bde5450ad571b6cc7";

//TODO
export const cesiumToken =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlNGZlZTQ0ZC1lN2YwLTRlMmMtODk0Ni0wZmE3M2NkY2ZhMWYiLCJpZCI6MTQ1NTA3LCJpYXQiOjE2ODYyMjcyNDB9.zXfjkA3IyDmaSm8cc45fpsy-PvcRGX44XfAd0A0Rf2E";

export function initMap(plugins) {
  window._AMapSecurityConfig = {
    securityJsCode: amapSecretkey,
  };
  return new Promise((resolve, reject) => {
    AMapLoader.load({
      key: amapKey,
      version: "2.0",
      plugins: plugins,
    })
      .then((AMap) => {
        resolve(AMap);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
