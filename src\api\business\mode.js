import request from '@/utils/request'

// 查询拍摄模式列表
export function listMode(query) {
  return request({
    url: '/business/mode/list',
    method: 'get',
    params: query
  })
}

// 查询拍摄模式详细
export function getMode(id) {
  return request({
    url: '/business/mode/' + id,
    method: 'get'
  })
}

// 新增拍摄模式
export function addMode(data) {
  return request({
    url: '/business/mode',
    method: 'post',
    data: data
  })
}

// 修改拍摄模式
export function updateMode(data) {
  return request({
    url: '/business/mode',
    method: 'put',
    data: data
  })
}

// 删除拍摄模式
export function delMode(id) {
  return request({
    url: '/business/mode/' + id,
    method: 'delete'
  })
}
