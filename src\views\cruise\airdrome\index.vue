<template>
    <div class="app-container">
        <div class="w-container">

            <div class="w-list" v-for="item in list" :key="item.airportId">
                <div class="w-img">
                    <img :src="item.airportPictureUrl">
                </div>
                <div class="w-cont">
                    <div class="w-left">
                        <div class="w-uva-name">
                            <div class="w-u">
                                <div class="w-dian w-dian-uav"></div>
                                <span class="w-name">收纳无人机SN码：{{ item.airportChildName }}</span>
                                <span class="w-status w-status-uav">{{ item.airportChildState | modeCode }}</span>
                            </div>
                            <div class="w-j">
                                <div class="w-dian w-dian-airport"></div>
                                <span class="w-name">机场SN码：{{ item.airportName }}</span>
                                <span class="w-status w-status-airport">机场状态:{{ item.modeCode | modeCode }}</span>
                            </div>
                        </div>
                        <div class="w-address">
                            <i class="el-icon-location"></i>
                            <span>{{ item.airportAddress || '--' }}</span>
                        </div>
                        <div class="w-info-section">
                            <div class="w-info-group w-info-row">
                                <div class="w-info-item">
                                    <span class="w-label">机场已执行任务数：</span>
                                    <span class="w-value">{{ item.airportChildCapacity || '--' }}次</span>
                                </div>
                                <div class="w-info-item">
                                    <span class="w-label">最后一次任务时间：</span>
                                    <span class="w-value">{{ item.airportEndTaskTime || '--' }}</span>
                                </div>
                                <div class="w-info-item">
                                    <span class="w-label">机场固件版本号：</span>
                                    <span class="w-value">{{ item.firmwareVersion || '--' }}</span>
                                </div>
                            </div>
                            <div class="w-info-group w-info-battery">
                                <div class="w-info-item">
                                    <span class="w-label">无人机电量：</span>
                                    <span class="w-value w-battery" :class="getBatteryClass(item.airportChildCapacity)">{{ item.airportChildCapacity ? item.airportChildCapacity + '%' : '--' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-right">
                        <div class="w-span">温度 {{item.environmentTemperature }}℃</div>
                        <div class="w-span">风速 {{ item.windSpeed }}m/s</div>
                        <div class="w-span">雨量 {{ item.rainfall }}mm/h</div>
                        <div class="w-btn"><el-button v-hasPermi="['device:airport:edit']" size="medium" type="text"
                                @click="toSecurity(item)">安全设置</el-button></div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</template>

<script>
import { listAirport, getRegeo, getWeatherInfo } from "@/api/cruise/airdrome";
export default {
    data() {
        return {
            list: [],
        }
    },
    created() {
        this.getData()
    },
    methods: {
        toSecurity(row) {
            this.$router.push("/cruise/airdrome-edit/security/" + row.airportId);
            // this.$router.push("/cruise/airdrome-edit/new-security/"+ row.airportId);
        },
        getBatteryClass(battery) {
            if (!battery && battery !== 0) return '';
            if (battery >= 80) return 'w-battery-high';
            if (battery >= 50) return 'w-battery-medium';
            if (battery >= 20) return 'w-battery-low';
            return 'w-battery-critical';
        },
        getData() {
            listAirport({ pageNum: 1, pageSize: 999 }).then(res => {
                console.log("机场列表", res)
                this.list = res.rows || []
                if (this.list.length != 0) {
                    this.list.forEach((item, index) => {
                        this.list[index].weather = ""
                        this.list[index].temperature = "--"
                        let location = item.longitude + "," + item.latitude
                        this.getWeather(location, index)
                    })
                }
            })
        },
        // 获取地址和天气
        getWeather(location, index) {
            let params = {
                location: location
            }
            getRegeo(params).then(res => {
                // console.log("获取的地址：" + index,res.data)
                let data = res.data
                if (data.status == "1") {
                    this.list[index].airportAddress = data.regeocode.formatted_address
                    this.$set(this.list, index, this.list[index])
                    getWeatherInfo({ city: data.regeocode.addressComponent.adcode }).then(resData => {
                        // console.log("天气信息："+ index,resData)
                        if (resData.data.status == '1') {
                            this.list[index].weather = resData.data.lives[0].weather
                            this.list[index].temperature = resData.data.lives[0].temperature
                            this.$set(this.list, index, this.list[index])
                        }
                    })
                } else {
                    this.list[index].weather = "--"
                    this.list[index].temperature = "--"
                    this.$set(this.list, index, this.list[index])
                }
            })
        }
    },
    filters: {
        modeCode(val) {
            if (val == 0) {
                return "空闲中";
            } else if (val == 1) {
                return "现场调试";
            } else if (val == 2) {
                return "远程调试";
            } else if (val == 3) {
                return "固件升级中";
            } else if (val == 4) {
                return "作业中";
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    background: #f5f5f5;
}

.w-container {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 10px;
    overflow: auto;
    padding: 20px;
}

.w-list {
    width: 100%;
    height: 180px;
    background: rgba(245, 245, 245, .5);
    display: flex;
    margin-bottom: 20px;

    .w-img {
        width: 300px;
        height: 180px;
        background: #fff;
        overflow: hidden;

        img {
            // width: 100%;
            height: 100%;
        }
    }

    .w-cont {
        width: calc(100% - 300px);
        height: 180px;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        font-size: 13px;

        .w-left {
            width: calc(100% - 150px);
            height: 100%;

            .w-uva-name {
                display: flex;
                width: 100%;
                height: 40px;
                align-items: center;
                margin-bottom: 8px;

                div {
                    display: flex;
                    align-items: center;
                }

                .w-j {
                    margin-left: 50px;
                    display: flex;
                }

                .w-dian {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-right: 10px;

                    &.w-dian-uav {
                        background: #409EFF;
                    }

                    &.w-dian-airport {
                        background: #67C23A;
                    }
                }

                .w-name {
                    margin-right: 12px;
                    font-weight: 600;
                    font-size: 14px;
                    color: #303133;
                }

                .w-status {
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;

                    &.w-status-uav {
                        background: rgba(64, 158, 255, 0.1);
                        color: #409EFF;
                    }

                    &.w-status-airport {
                        background: rgba(103, 194, 58, 0.1);
                        color: #67C23A;
                    }
                }
            }

            .w-address {
                height: 32px;
                line-height: 32px;
                margin-bottom: 12px;
                color: #606266;
                font-size: 13px;

                i {
                    margin-right: 8px;
                    color: #909399;
                }
            }

            .w-info-section {
                display: flex;
                flex-direction: column;
                gap: 12px;

                .w-info-group {
                    display: flex;
                    gap: 6px;

                    &.w-info-row {
                        flex-direction: row;
                        justify-content: space-between;

                        .w-info-item {
                            flex: 1;
                            margin-right: 20px;

                            &:last-child {
                                margin-right: 0;
                            }
                        }
                    }

                    &.w-info-battery {
                        flex-direction: column;
                    }

                    .w-info-item {
                        display: flex;
                        align-items: center;
                        height: 28px;

                        .w-label {
                            color: #606266;
                            font-size: 13px;
                            min-width: 120px;
                            white-space: nowrap;
                        }

                        .w-value {
                            color: #303133;
                            font-size: 13px;
                            font-weight: 500;

                            &.w-battery {
                                padding: 2px 6px;
                                border-radius: 4px;
                                font-weight: 600;

                                &.w-battery-high {
                                    background: rgba(103, 194, 58, 0.1);
                                    color: #67C23A;
                                }

                                &.w-battery-medium {
                                    background: rgba(230, 162, 60, 0.1);
                                    color: #E6A23C;
                                }

                                &.w-battery-low {
                                    background: rgba(245, 108, 108, 0.1);
                                    color: #F56C6C;
                                }

                                &.w-battery-critical {
                                    background: rgba(245, 108, 108, 0.2);
                                    color: #F56C6C;
                                    animation: pulse 2s infinite;
                                }
                            }
                        }
                    }
                }
            }
        }

        .w-right {
            width: 150px;
            height: 100%;

            div {
                height: 35px;
                line-height: 35px;
                text-align: right;
            }
        }
    }
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
    }
}
</style>