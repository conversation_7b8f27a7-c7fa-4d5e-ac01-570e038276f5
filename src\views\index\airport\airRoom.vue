<template>
    <div :class="className" :style="{height:height,width:width}">
        <div class="w-height">
            <h3>航线统计</h3>
            <img @click="tiao('/cruise/airline')" src="@/assets/images/index/godtl.png" >
        </div>
        <div class="w-box">
            <div ref="chart" style="width:100%;height:100%;"></div>
        </div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts';
  import { indexRouteDroneStatistics } from '@/api/index/index'
  import resize from '../mixins/resize'
  export default {
    mixins: [resize],
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '350px'
      }
    },
    data() {
      return {
        tableData: [],
        loading: false,
        chart: null,
      }
    },
    created() {
        this.getData()
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        tiao(url){
            this.$router.push(url)
        },
        getData(){
            this.loading = true
            let params = {}
            indexRouteDroneStatistics(params).then(res => {
                console.log("机场统计",res)
                this.tableData = res.data || []
                this.$nextTick(() => {
                    this.initChart()
                })
                this.loading = false
            }).catch(e => {
                this.loading = false
            })
        },
        initChart() {
            this.chart = echarts.init(this.$refs.chart)

            this.chart.setOption({
                tooltip: {
                    trigger: 'item',
                },
                series: [
                    {
                        name: '航线统计',
                        type: 'pie',
                        radius: '70%',
                        data: this.tableData,
                        animationEasing: 'cubicInOut',
                        animationDuration: 1500,
                        itemStyle: {
                            color: function(data) {
                                var colorList = [
                                    '#79a1f1',
                                    '#fd7c7c'
                                ]
                                return colorList[data.dataIndex];
                            }
                        }
                    }
                ]
            })
        }
    }
  }
  </script>

  <style lang="scss" scoped>
    .w-height {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(270deg, #BFD3F7 0%, #6694f0 100%);
        border-radius: 6px 6px 0px 0px;
        color: #FFFFFF;
        padding: 0 15px;
        h3 {
            font-size: 16px;
            font-weight: 700;
        }
        img {
            width: 25px;
        }
    }
    .w-box {
        width: 100%;
        height: calc(100% - 40px);
        padding: 10px 15px;
    }
  </style>
  