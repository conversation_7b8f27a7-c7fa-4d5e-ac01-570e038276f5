import request from "@/utils/request";

// 获取无人机列表
export function uvaList(query) {
  return request({
    url: "/device/DroneInfo/list",
    method: "get",
    params: query,
  });
}
// 新增无人机信息
export function addUva(data) {
  return request({
    url: "/device/DroneInfo",
    method: "post",
    data,
  });
}
// 修改无人机信息
export function editUva(data) {
  return request({
    url: "/device/DroneInfo",
    method: "put",
    data,
  });
}

// 获取电池列表
export function batteryList(query) {
  return request({
    url: "/device/battery/list",
    method: "get",
    params: query,
  });
}
