alert(getHeading(Cesium.Cartesian3.fromDegrees(133.0, 30.0), Cesium.Cartesian3.fromDegrees(130.0, 80.0)));
 
// 根据两个坐标点,获取Heading(朝向)
function getHeading(pointA, pointB) {
    //建立以点A为原点，X轴为east,Y轴为north,Z轴朝上的坐标系
    const transform = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
    //向量AB
    const positionvector = Cesium.Cartesian3.subtract(pointB, pointA, new Cesium.Cartesian3());
    //因transform是将A为原点的eastNorthUp坐标系中的点转换到世界坐标系的矩阵
    //AB为世界坐标中的向量
    //因此将AB向量转换为A原点坐标系中的向量，需乘以transform的逆矩阵。
    const vector = Cesium.Matrix4.multiplyByPointAsVector(Cesium.Matrix4.inverse(transform, new Cesium.Matrix4()), positionvector, new Cesium.Cartesian3());
    //归一化
    const direction = Cesium.Cartesian3.normalize(vector, new Cesium.Cartesian3());
    //heading
    const heading = Math.atan2(direction.y, direction.x) - Cesium.Math.PI_OVER_TWO;
    return Cesium.Math.TWO_PI - Cesium.Math.zeroToTwoPi(heading);
}

/**
* 
* @param {*} lon 经度
* @param {*} lat 纬度
* @param {*} angle 角度 (0~360度)
* @param {*} distance 距离 (米)
*
*/
function get_another_point(lon, lat, angle, distance) {
	// WGS84坐标系
	var a = 6378137;	// 赤道半径
	var b = 6356752.3142;	// 短半径
	var f = 1 / 298.257223563;	// 扁率
 
	var alpha1 = angle * (Math.PI / 180)
	var sinAlpha1 = Math.sin(alpha1);
	var cosAlpha1 = Math.cos(alpha1);
	var tanU1 = (1 - f) * Math.tan(lat * (Math.PI / 180));
	var cosU1 = 1 / Math.sqrt((1 + tanU1 * tanU1)), sinU1 = tanU1 * cosU1;
	var sigma1 = Math.atan2(tanU1, cosAlpha1);
	var sinAlpha = cosU1 * sinAlpha1;
	var cosSqAlpha = 1 - sinAlpha * sinAlpha;
	var uSq = cosSqAlpha * (a * a - b * b) / (b * b);
	var A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
	var B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));
	var sigma = distance / (b * A), sigmaP = 2 * Math.PI;
	while (Math.abs(sigma - sigmaP) > 1e-12) {
		var cos2SigmaM = Math.cos(2 * sigma1 + sigma);
		var sinSigma = Math.sin(sigma);
		var cosSigma = Math.cos(sigma);
		var deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
			B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));
		sigmaP = sigma;
		sigma = distance / (b * A) + deltaSigma;
	}
 
	var tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1;
	var lat2 = Math.atan2(sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,
		(1 - f) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp));
	var lambda = Math.atan2(sinSigma * sinAlpha1, cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1);
	var C = f / 16 * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
	var L = lambda - (1 - C) * f * sinAlpha *
		(sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));
 
	// var revAz = Math.atan2(sinAlpha, -tmp); // final bearing
 
	return { lon: Number(lon) + L * (180 / Math.PI), lat: lat2 * (180 / Math.PI) };
}

