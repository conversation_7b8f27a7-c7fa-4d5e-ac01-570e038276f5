import request from '@/utils/request'

//进行中的列表
export function indexActionStatistics(params) {
    return request({
      url: '/index/actionStatistics',
      method: 'get',
      params
    })
}

//行动次数环比
export function indexActionYearStatistics(params) {
    return request({
      url: '/index/actionYearStatistics',
      method: 'get',
      params
    })
}

//无人机机型统计
export function indexDroneTypeStatistics(params) {
    return request({
      url: '/index/droneTypeStatistics',
      method: 'get',
      params
    })
}

//机场统计
export function indexRouteDroneStatistics(params) {
    return request({
      url: '/index/routeDroneStatistics',
      method: 'get',
      params
    })
}

//未来7天循环任务统计
export function indexTaskFutureStatistics(params) {
    return request({
      url: '/index/taskFutureStatistics',
      method: 'get',
      params
    })
}

// 进行中的任务列表
export function indexTaskStatistics(params) {
    return request({
      url: '/index/taskStatistics',
      method: 'get',
      params
    })
}

// 巡航任务环比
export function indexTaskTotalMonthly(params) {
    return request({
      url: '/index/taskTotalMonthly',
      method: 'get',
      params
    })
}

