<template>
    <div :class="className" :style="{height:height,width:width}">
        <div class="w-height">
            <h3>年度行动环比</h3>
            <!-- <img @click="tiao('/command/moveabout')" src="@/assets/images/index/godtl.png" > -->
        </div>
        <div class="w-box">
            <div ref="actionYear" style="width:100%;height:100%;"></div>
        </div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts';
  import { indexActionYearStatistics } from '@/api/index/index'
  import resize from '../mixins/resize'
  export default {
    mixins: [resize],
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '350px'
      }
    },
    data() {
      return {
        tableData: [],
        loading: false,
        chart: null,
      }
    },
    beforeDestroy() {
        if (!this.chart) {
        return
        }
        this.chart.dispose()
        this.chart = null
    },
    mounted() {
        this.getData()
    },
    beforeDestroy() {
        if (!this.chart) {
        return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        tiao(url){
            this.$router.push(url)
        },
        getData(){
            let params = {}
            this.loading = true
            indexActionYearStatistics(params).then(res => {
                console.log("年度行动环比",res)
                // this.tableData = res.rows || []
                let data = res.data
                this.$nextTick(() => {
                    this.initChart(data)
                })
                this.loading = false
            }).catch(e => {
                this.loading = false
            })
        },
        initChart(data) {
            this.chart = echarts.init(this.$refs.actionYear)
            this.setOptions(data)
        },
        setOptions(data) {
            this.chart.setOption(
                {
                    xAxis: {
                    data: data.month,
                    boundaryGap: false,
                    axisTick: {
                        show: false
                    }
                    },
                    grid: {
                        left: 20,
                        right: 20,
                        bottom: 20,
                        top: 30,
                        containLabel: true
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross'
                        },
                        padding: [5, 10]
                    },
                    yAxis: {
                        axisTick: {
                            show: false
                        }
                    },
                    series: [
                        {
                            name: '今年', 
                            itemStyle: {
                                normal: {
                                    color: '#FF005A',
                                    lineStyle: {
                                        color: '#FF005A',
                                        width: 2
                                    }
                                }
                            },
                            smooth: true,
                            type: 'line',
                            data: data.this_year,
                            animationDuration: 2800,
                            animationEasing: 'cubicInOut'
                        },
                        {
                            name: '去年',
                            smooth: true,
                            type: 'line',
                            itemStyle: {
                                normal: {
                                color: '#3888fa',
                                lineStyle: {
                                    color: '#3888fa',
                                    width: 2
                                },
                                areaStyle: {
                                    color: '#f3f8ff'
                                }
                                }
                            },
                            data: data.last_year,
                            animationDuration: 2800,
                            animationEasing: 'quadraticOut'
                        }
                ]
            })
        }
    }
  }
  </script>

  <style lang="scss" scoped>
    .w-height {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(270deg, #BFD3F7 0%, #6694f0 100%);
        border-radius: 6px 6px 0px 0px;
        color: #FFFFFF;
        padding: 0 15px;
        h3 {
            font-size: 16px;
            font-weight: 700;
        }
        img {
            width: 25px;
        }
    }
    .w-box {
        width: 100%;
        height: calc(100% - 40px);
        padding: 10px 15px;
    }
  </style>
  