<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="航线名称" prop="routesName">
        <el-input
          v-model="queryParams.routesName"
          placeholder="请输入航线名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="机场" prop="airportId">
          <el-select
            v-model="queryParams.airportId"
            placeholder="机场"
            clearable
            style="width: 240px"
          >
            <el-option label="全部" :value="undefined"/>
            <el-option v-for="item in airportList" :key="item.airportId" :label="item.airportName" :value="item.airportId"/>
          </el-select>
        </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd(0)"
          v-hasPermi="['routes:info:edit']"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <div class="w-table" v-loading="loading">
      <el-empty description="暂无数据" v-if="total == 0"></el-empty>

      <el-row :gutter="20" v-else>
        <el-col :span="12" v-for="item in tableList" :key="item.routesId">
          <div class="w-box" @click="handleAdd(item.routesId)">
            <div class="w-left">
              <img :src="item.routesPreviewUrl" />
            </div>
            <div class="w-right">
              <p class="w-title">{{ item.routesName }}</p>
              <el-tooltip
                class="item"
                effect="dark"
                :disabled="item.routesStartPoint > 16"
                :content="item.routesStartPoint"
                placement="top-start"
              >
                <p>起点：{{ item.routesStartPoint }}</p>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                :disabled="item.routesEndPoint > 16"
                :content="item.routesEndPoint"
                placement="top-start"
              >
                <p>终点：{{ item.routesEndPoint }}</p>
              </el-tooltip>
              <p>创建时间：{{ item.createTime }}</p>
              <p>机场：{{ item.airportName }}</p>
              <i
                v-hasPermi="['routes:info:remove']"
                class="el-icon-delete w-delete"
                @click.stop="handleDelete(item)"
              ></i>
            </div>
          </div>
        </el-col>
      </el-row>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <el-dialog
      title="新建航线"
      :visible.sync="openAdd"
      width="640px"
      append-to-body
    >
      <el-form
        ref="addform"
        :model="addform"
        :rules="addrules"
        label-width="80px"
      >
        <el-form-item label="航线名称" prop="routesName">
          <el-input v-model="addform.routesName" placeholder="请输入航线名称" />
        </el-form-item>
        <el-form-item label="机场" prop="airportId">
          <el-select
          style="width: 100%;"
            v-model="addform.airportId"
            placeholder="请选择机场"
            clearable
            multiple
          >
            <el-option v-for="item in airportList" :key="item.airportId" :label="item.airportName" :value="item.airportId"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
import { routesList, DeleteRoutes } from "@/api/cruise/airline";
import { listAirport } from "@/api/cruise/airdrome";
export default {
  name: "Dict",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 预案表格数据
      tableList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        routesName: undefined,
          airportId: undefined,
      },
      addform: {
        routesName: undefined,
        airportId: undefined,
      },
      addrules: {
        routesName: [
          { required: true, message: "请输入航线名称", trigger: "blur" }
        ],
        airportId: [
          { required: true, message: "请选择机场", trigger: "change" }
        ]
      },
      openAdd: false,
      airportList: []
    };
  },
  created() {
    this.getList();
    this.getAirport()
  },
  methods: {
    getAirport(){
        listAirport({pageNum: 1,pageSize: 999}).then(res => {
            console.log("机场列表",res)
            this.airportList = res.rows || []
        })
    },
    reset() {
      this.addform = {
        routesName: undefined,
        airportId: undefined
      };
      this.resetForm("addform");
    },
    submitForm() {
        this.$refs["addform"].validate(valid => {
            if(valid) {
                // 如果是数组，则转换为逗号分隔的字符串
                const airportId = Array.isArray(this.addform.airportId) 
                    ? this.addform.airportId.join(',') 
                    : this.addform.airportId;
                    
                this.$router.push({
                    path: "/cruise/airline-edit/edit/0",
                    query: {
                        routesName: this.addform.routesName,
                        airportId: airportId
                    }
                })
            }
        })
    },
    cancel() {
        this.openAdd = false
        this.reset()
    },
    /** 查询预案列表 */
    getList() {
      this.loading = true;
      routesList(
        this.addDateRange(this.queryParams, this.dateRange, "CreateTime")
      ).then((response) => {
        console.log("航线", response);
        this.tableList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(id) {
        if(id == 0) {
            this.openAdd = true
        } else {
            this.$router.push("/cruise/airline-edit/edit/" + id)
        }
      // console.log(1)
      // this.$router.push("/command/plan-edit/edit/"+ id);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除航线名称为"' + row.routesName + '"的数据项？')
        .then(function () {
          return DeleteRoutes(row.routesId);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
  
  <style lang="scss" scoped>
.w-table {
  width: 100%;
  min-height: calc(100% - 51px);
}
.w-box {
  height: 195px;
  position: relative;
  cursor: pointer;
  border-radius: 5px;
  overflow: hidden;

  .w-left {
    position: absolute;
    width: 250px;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 999;
    overflow: hidden;
    img {
      height: 100%;
      transform: scale(2);
      transform-origin: center;
    }
  }
  .w-right {
    position: absolute;
    width: calc(100% - 250px);
    height: 100%;
    right: 0;
    top: 0;
    background: #f7f7f7;
    padding: 10px 20px;
    overflow: hidden;
    p {
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      font-size: 13px;
      margin: 15px 0;
    }
    .w-title {
      font-size: 14px;
      margin: 10px 0;
    }
    .w-delete {
      position: absolute;
      bottom: 20px;
      right: 20px;
      color: #999;
    }
    .w-delete:hover {
      color: #1890ff;
    }
  }
}

::v-deep {
  .el-col {
    margin-bottom: 20px;
  }
}
</style>