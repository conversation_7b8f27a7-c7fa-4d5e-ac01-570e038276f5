import request from '@/utils/request'

// 获取行动列表
export function listAction(query) {
    return request({
      url: '/plan/action/list',
      method: 'get',
      params: query
    })
}

// 删除行动
export function DeleteAction(id) {
  return request({
      url: '/plan/action/' + id,
      method: 'delete',
  })
}

// 新增行动
export function addAction(data) {
  return request({
      url: "/plan/action",
      method: "post",
      data
  })
}

// 修改行动
export function editAction(data) {
  return request({
      url: "/plan/action",
      method: "put",
      data
  })
}

// 行动详情
export function detAction(id) {
  return request({
    url: '/plan/action/' + id,
    method: 'get',
  })
}

// 行动方案详情
export function detActionScheme(id) {
  return request({
    url: '/plan/scheme/' + id,
    method: 'get',
  })
}

// 修改行动方案
export function editActionScheme(data) {
  return request({
      url: "/plan/scheme",
      method: "put",
      data
  })
}

// 行动标记列表
export function listMark(query) {
  return request({
    url: '/action/mark/list',
    method: 'get',
    params: query
  })
}

// 行动标记新增
export function addActionMark(data) {
  return request({
      url: "/action/mark",
      method: "post",
      data
  })
}

// 行动标记修改
export function editMark(data) {
  return request({
      url: "/action/mark",
      method: "put",
      data
  })
}

// 删除行动
export function DeleteMark(id) {
  return request({
      url: '/action/mark/' + id,
      method: 'delete',
  })
}

// 行动结束
export function endAction(data) {
  return request({
      url: "/plan/action/end",
      method: "put",
      data
  })
}

// 人员列表
export function userSelectUse(params) {
  return request({
      url: "/plan/user/selectUser",
      method: "get",
      params
  })
}

// 获取行动中的飞手及设备列表
export function userGetFlyingInfo(params) {
  return request({
      url: "/plan/user/getFlyingInfo",
      method: "get",
      params
  })
}

// 获取行动中的特勤人员
export function userGetSecretInfo(params) {
  return request({
      url: "/plan/user/getSecretInfo",
      method: "get",
      params
  })
}

// 新增行动记录图片
export function addRecordMedia(data) {
  return request({
      url: "/plan/record/media",
      method: "post",
      data
  })
}


// 是否开启录像
export function actionRecordVideo(data) {
  return request({
      url: "/plan/action/RecordVideo",
      method: "post",
      data
  })
}

// 获取录音
export function planActionMp3List(params) {
  return request({
      url: "/planAction/mp3/list",
      method: "get",
      params
  })
}

// 修改录音
export function planActionMp3Update(data) {
  return request({
      url: "/planAction/mp3",
      method: "put",
      data
  })
}

// 新增录音
export function planActionMp3Add(data) {
  return request({
      url: "/planAction/mp3",
      method: "post",
      data
  })
}

// 删除录音
export function planActionMp3Delete(id) {
  return request({
      url: "/planAction/mp3/" + id,
      method: "delete",
  })
}
