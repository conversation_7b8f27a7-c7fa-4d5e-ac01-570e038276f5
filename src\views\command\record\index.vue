<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="行动名称" prop="actionName">
          <el-input
            v-model="queryParams.actionName"
            placeholder="请输入行动名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="飞手名称" prop="pilotName">
          <el-input
            v-model="queryParams.pilotName"
            placeholder="请输入飞手名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
  
      <el-table v-loading="loading" :data="tableList">
        <el-table-column label="行动名称" align="center" prop="actionName" />
        <el-table-column label="方案名称" align="center" prop="schemeName" :show-overflow-tooltip="true" />
        <el-table-column label="飞手名称" align="center" prop="pilotName" :show-overflow-tooltip="true" />
        <el-table-column label="设备名称" align="center" prop="droneName" :show-overflow-tooltip="true" />
        <el-table-column label="行动内容" align="center" prop="content" width="180"></el-table-column>
        <el-table-column label="占用空间" align="center" prop="mediaSize" >
          <template slot-scope="scope">
            <span>{{scope.row.mediaSize | mediaSize}}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180"></el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleDet(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </template>
  
  <script>
  import { listRecord, recordDelete } from "@/api/command/record";
  export default {
    name: "Dict",
    data() {
      return {
        // 遮罩层
        loading: false,
        // 总条数
        total: 0,
        // 字典表格数据
        tableList: [],
        // 是否显示弹出层
        open: false,
        // 日期范围
        dateRange: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          pilotName: undefined,
          actionName: undefined
        },
      };
    },
    created() {
      this.getList();
    },
    methods: {
      getList() {
        this.loading = true;
        listRecord(this.addDateRange(this.queryParams, this.dateRange,'CreateTime')).then(response => {
          console.log("飞行记录列表",response)
          this.tableList = response.rows;
          this.total = response.total;
          this.loading = false;
          }
        );
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      /** 修改按钮操作 */
      handleDet(row) {
        this.$router.push('/command/record-det/record/' + row.id)
      },
      handleDelete(row){
        this.$modal.confirm('删除后无法恢复是否删除？').then(function() {
          return recordDelete(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      }
    },
    filters:{
      mediaSize(val){
        if(val) {
          if(val < 1024) {
            return val + 'b';
          } else if( val < 1024 * 1024) {
            return Math.round((val/1024) * 100) / 100 + 'Kb';
          } else if(val < 1024 * 1024 * 1024) {
            return Math.round((val/1024/1024) * 100) / 100 + 'Mb';
          } else {
            return Math.round((val/1024/1024/1024) * 100) / 100 + 'Gb';
          }
        } else {
          return val;
        }
      }
    }
  };
  </script>