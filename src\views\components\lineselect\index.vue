<template>
    <div class="w-box" @click="handleClick">
        <span v-if="!isselect">{{label}}</span>
        <span v-else>{{title}}</span>
        <div class="w-option" v-if="show">
            <div @click="handleOptionClick(item)" v-for="item in option" :key="item.value">{{item.label}}</div>
        </div>
    </div>
</template>

<script>
export default {
    props: ["option","value","isselect","title"],
    model: {
        prop: "value",
        event: "update:value"
    },
    data (){
        return {
            label: "",
            show:false,
        }
    },
    created(){
        if(this.isselect) {
            this.label = this.title
        } else {
            let index = this.option.findIndex(item => item.value == this.value)
            if(index != -1) {
                this.label = this.option[index].label
            } else {
                this.label = this.value
            }
        }
        
    },
    methods: {
        handleClick(){
            this.show = !this.show
        },
        handleOptionClick(item){
            if(this.isselect) {
                this.$emit("change",item.value)
            } else {
                this.label = item.label
                this.$emit("update:value",item.value)
            }
            
        }
    },
    watch: {
        value(val){
            if(this.isselect) {
                this.label = this.title
            } else {
                let index = this.option.findIndex(item => item.value == this.value)
                if(index != -1) {
                    this.label = this.option[index].label
                } else {
                    this.label = this.value
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.w-box {
    width: 100%;
    height: 35px;
    line-height: 35px;
    position: relative;
    text-align: center;
    border-radius: 5px;
    background-color: #59555f;
    font-size: 14px;
    // z-index: 100;
    cursor: pointer;
}

.w-option {
    width: 100%;
    position: absolute;
    top: 40px;
    left: 0;
    background: #212121;
    border-radius: 5px;
    overflow: hidden;
    z-index: 1002;
    div {
        width: 100%;
        height: 35px;
        cursor: pointer;
    }
    &>div:hover {
        background: #59555f;
    }
}
</style>