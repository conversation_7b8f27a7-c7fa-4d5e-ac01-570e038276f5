// import AgoraRTC from "agora-rtc-sdk-ng";

// var AgoraRTCClient = AgoraRTC.createClient({mode: "rtc", codec: "vp8"})

// var localTracks = {
//     audioTrack: null
// }

// var remoteUsers = {};

// AgoraRTC.onAutoplayFailed = () => {
//     alert("click to start autoplay!");
// };
// AgoraRTC.onMicrophoneChanged = async changedDevice => {
//     console.log("changedDevice",changedDevice)
//     // When plugging in a device, switch to a device that is newly plugged in.
//     if (changedDevice.state === "ACTIVE") {
//         _this.audioTrack.setDevice(changedDevice.device.deviceId);
//         // Switch to an existing device when the current device is unplugged.
//     } else if (changedDevice.device.label === _this.audioTrack.getTrackLabel()) {
//         const oldMicrophones = await AgoraRTC.getMicrophones();
//         oldMicrophones[0] && _this.audioTrack.setDevice(oldMicrophones[0].deviceId);
//     }
// };


// export function join(channel, token, uid){

// }