<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="航线名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入航线名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="航线ID" prop="waylineId">
        <el-input
          v-model="queryParams.waylineId"
          placeholder="请输入航线ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="飞行器型号" prop="droneModelKey">
        <el-input
          v-model="queryParams.droneModelKey"
          placeholder="请输入飞行器型号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="负载型号" prop="payloadModelKeys">
        <el-input
          v-model="queryParams.payloadModelKeys"
          placeholder="请输入负载型号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作空间ID" prop="workspaceId">
        <el-input
          v-model="queryParams.workspaceId"
          placeholder="请输入工作空间ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="航线文件签名" prop="sign">
        <el-input
          v-model="queryParams.sign"
          placeholder="请输入航线文件签名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="Whether the file is favorited or not." prop="favorited">
        <el-input
          v-model="queryParams.favorited"
          placeholder="请输入Whether the file is favorited or not."
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="航线文件模板类型 0: waypoint;" prop="templateTypes">
        <el-input
          v-model="queryParams.templateTypes"
          placeholder="请输入航线文件模板类型 0: waypoint;"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件key" prop="objectKey">
        <el-input
          v-model="queryParams.objectKey"
          placeholder="请输入文件key"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入创建人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-input
          v-model="queryParams.createTime"
          placeholder="请输入创建时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间" prop="updateTime">
        <el-input
          v-model="queryParams.updateTime"
          placeholder="请输入修改时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['business:waylinefile:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:waylinefile:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:waylinefile:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:waylinefile:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="waylinefileList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="" align="center" prop="id" />
      <el-table-column label="航线名称" align="center" prop="name" />
      <el-table-column label="航线ID" align="center" prop="waylineId" />
      <el-table-column label="飞行器型号" align="center" prop="droneModelKey" />
      <el-table-column label="负载型号" align="center" prop="payloadModelKeys" />
      <el-table-column label="工作空间ID" align="center" prop="workspaceId" />
      <el-table-column label="航线文件签名" align="center" prop="sign" />
      <el-table-column label="Whether the file is favorited or not." align="center" prop="favorited" />
      <el-table-column label="航线文件模板类型 0: waypoint;" align="center" prop="templateTypes" />
      <el-table-column label="文件key" align="center" prop="objectKey" />
      <el-table-column label="创建人" align="center" prop="userName" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="修改时间" align="center" prop="updateTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['business:waylinefile:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['business:waylinefile:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改Wayline file information对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="航线名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入航线名称" />
        </el-form-item>
        <el-form-item label="航线ID" prop="waylineId">
          <el-input v-model="form.waylineId" placeholder="请输入航线ID" />
        </el-form-item>
        <el-form-item label="飞行器型号" prop="droneModelKey">
          <el-input v-model="form.droneModelKey" placeholder="请输入飞行器型号" />
        </el-form-item>
        <el-form-item label="负载型号" prop="payloadModelKeys">
          <el-input v-model="form.payloadModelKeys" placeholder="请输入负载型号" />
        </el-form-item>
        <el-form-item label="工作空间ID" prop="workspaceId">
          <el-input v-model="form.workspaceId" placeholder="请输入工作空间ID" />
        </el-form-item>
        <el-form-item label="航线文件签名" prop="sign">
          <el-input v-model="form.sign" placeholder="请输入航线文件签名" />
        </el-form-item>
        <el-form-item label="Whether the file is favorited or not." prop="favorited">
          <el-input v-model="form.favorited" placeholder="请输入Whether the file is favorited or not." />
        </el-form-item>
        <el-form-item label="航线文件模板类型 0: waypoint;" prop="templateTypes">
          <el-input v-model="form.templateTypes" placeholder="请输入航线文件模板类型 0: waypoint;" />
        </el-form-item>
        <el-form-item label="文件key" prop="objectKey">
          <el-input v-model="form.objectKey" placeholder="请输入文件key" />
        </el-form-item>
        <el-form-item label="创建人" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入创建人" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWaylinefile, getWaylinefile, delWaylinefile, addWaylinefile, updateWaylinefile } from "@/api/business/waylinefile";

export default {
  name: "Waylinefile",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // Wayline file information表格数据
      waylinefileList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        waylineId: null,
        droneModelKey: null,
        payloadModelKeys: null,
        workspaceId: null,
        sign: null,
        favorited: null,
        templateTypes: null,
        objectKey: null,
        userName: null,
        createTime: null,
        updateTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "航线名称不能为空", trigger: "blur" }
        ],
        waylineId: [
          { required: true, message: "航线ID不能为空", trigger: "blur" }
        ],
        droneModelKey: [
          { required: true, message: "飞行器型号不能为空", trigger: "blur" }
        ],
        workspaceId: [
          { required: true, message: "工作空间ID不能为空", trigger: "blur" }
        ],
        sign: [
          { required: true, message: "航线文件签名不能为空", trigger: "blur" }
        ],
        favorited: [
          { required: true, message: "Whether the file is favorited or not.不能为空", trigger: "blur" }
        ],
        templateTypes: [
          { required: true, message: "航线文件模板类型 0: waypoint;不能为空", trigger: "blur" }
        ],
        objectKey: [
          { required: true, message: "文件key不能为空", trigger: "blur" }
        ],
        userName: [
          { required: true, message: "创建人不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "修改时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询Wayline file information列表 */
    getList() {
      this.loading = true;
      listWaylinefile(this.queryParams).then(response => {
        this.waylinefileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        waylineId: null,
        droneModelKey: null,
        payloadModelKeys: null,
        workspaceId: null,
        sign: null,
        favorited: null,
        templateTypes: null,
        objectKey: null,
        userName: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加Wayline file information";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWaylinefile(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改Wayline file information";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWaylinefile(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWaylinefile(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除Wayline file information编号为"' + ids + '"的数据项？').then(function() {
        return delWaylinefile(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/waylinefile/export', {
        ...this.queryParams
      }, `waylinefile_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
