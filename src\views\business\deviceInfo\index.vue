<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="无人机sn" prop="sn">
        <el-input v-model="queryParams.sn" placeholder="请输入无人机sn" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机场sn" prop="airportSn">
        <el-input v-model="queryParams.airportSn" placeholder="请输入机场sn" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="无人机型号" prop="mode">
        <el-input v-model="queryParams.mode" placeholder="请输入无人机型号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="无人机状态" prop="state">
        <el-input v-model="queryParams.state" placeholder="请输入无人机状态" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="无人机电量" prop="capacity">
        <el-input v-model="queryParams.capacity" placeholder="请输入无人机电量" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="无人机名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入无人机名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['business:deviceInfo:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['business:deviceInfo:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['business:deviceInfo:remove']">删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['business:deviceInfo:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceInfoList" >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="id" align="center" prop="id" /> -->
      <el-table-column label="无人机名称" align="center" prop="name" />
      <el-table-column label="无人机sn" align="center" prop="sn" />
      <el-table-column label="机场sn" align="center" prop="airportSn" />
      <el-table-column label="无人机型号" align="center" prop="mode" />
      <el-table-column label="无人机状态" align="center" prop="state" />
      <el-table-column label="无人机电量" align="center" prop="capacity" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['business:deviceInfo:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['business:deviceInfo:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改无人机设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="无人机名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入无人机名称" />
        </el-form-item>
        <el-form-item label="无人机sn" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入无人机sn" />
        </el-form-item>
        <el-form-item label="机场sn" prop="airportSn">
          <el-input v-model="form.airportSn" placeholder="请输入机场sn" />
        </el-form-item>
        <el-form-item label="无人机型号" prop="mode">
          <el-input v-model="form.mode" placeholder="请输入无人机型号" />
        </el-form-item>
        <el-form-item label="无人机状态" prop="state">
          <el-input v-model="form.state" placeholder="请输入无人机状态" />
        </el-form-item>
        <el-form-item label="无人机电量" prop="capacity">
          <el-input v-model="form.capacity" placeholder="请输入无人机电量" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDeviceInfo, getDeviceInfo, delDeviceInfo, addDeviceInfo, updateDeviceInfo } from "@/api/business/deviceInfo";

export default {
  name: "DeviceInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 无人机设备表格数据
      deviceInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sn: null,
        airportSn: null,
        mode: null,
        state: null,
        capacity: null,
        name: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询无人机设备列表 */
    getList() {
      this.loading = true;
      listDeviceInfo(this.queryParams).then(response => {
        this.deviceInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sn: null,
        airportSn: null,
        mode: null,
        state: null,
        capacity: null,
        name: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加无人机设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDeviceInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改无人机设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDeviceInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDeviceInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除无人机设备编号为"' + ids + '"的数据项？').then(function () {
        return delDeviceInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/deviceInfo/export', {
        ...this.queryParams
      }, `deviceInfo_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
