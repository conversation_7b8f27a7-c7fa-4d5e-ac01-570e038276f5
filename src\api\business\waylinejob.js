import request from '@/utils/request'

// 查询Wayline mission information of the dock.列表
export function listWaylinejob(query) {
  return request({
    url: '/business/waylinejob/list',
    method: 'get',
    params: query
  })
}

// 查询Wayline mission information of the dock.详细
export function getWaylinejob(id) {
  return request({
    url: '/business/waylinejob/' + id,
    method: 'get'
  })
}

// 新增Wayline mission information of the dock.
export function addWaylinejob(data) {
  return request({
    url: '/business/waylinejob',
    method: 'post',
    data: data
  })
}

// 修改Wayline mission information of the dock.
export function updateWaylinejob(data) {
  return request({
    url: '/business/waylinejob',
    method: 'put',
    data: data
  })
}

// 删除Wayline mission information of the dock.
export function delWaylinejob(id) {
  return request({
    url: '/business/waylinejob/' + id,
    method: 'delete'
  })
}

// 取消任务
export function cancelTask(jobId) {
  return request({
    url: '/business/waylinejob/canceltask/' + jobId,
    method: 'get'
  })
}
