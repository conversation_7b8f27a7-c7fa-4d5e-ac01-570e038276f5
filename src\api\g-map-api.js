import axios from 'axios';
import { amapServerKey } from "@/utils/use-g-map"

// 根据经纬度获取地址信息
export function getRegeo(query) {
    return axios({
        url: "https://restapi.amap.com/v3/geocode/regeo",
        method: "get",
        params: {
            key: amapServerKey,
            ...query
        }
    })
}

// 根据区域获取天气信息
export function getWeatherInfo(query) {
    return axios({
        url: "https://restapi.amap.com/v3/weather/weatherInfo",
        method: "get",
        params: {
            key: amapServerKey,
            ...query
        }
    })
}

// 输入提示API服务,地址提示
export function getAssistantInputtips(query){
    return axios({
        url: "https://restapi.amap.com/v3/assistant/inputtips",
        method: "get",
        params: {
            key: amapServerKey,
            ...query
        }
    })
}

// 根据ip定位
export function getCurrentLocation(){
    return axios({
        url: "https://restapi.amap.com/v3/ip",
        method: "get",
        params: {
            key: amapServerKey
        }
    })
}

// wgs84 转高德
export function getWgs84togcj02(locations){
    return axios({
        url: "https://restapi.amap.com/v3/assistant/coordinate/convert",
        method: "get",
        params: {
            key: amapServerKey,
            locations,
            coordsys: 'gps'
        }
    })
}