<template>
    <div class="app-container">
        <div class="w-title">
            <div>行动编辑</div>
            <div>
                <el-button type="primary" @click="addSave">提交</el-button>
                <el-button @click="cancel">取消</el-button>
            </div>
        </div>
        <div class="w-box">
            <div>
                <el-form ref="form1" label-position="top" label-width="80px" :model="fromData" :rules="rules">
                    <el-form-item label="行动名称" prop="name">
                        <el-input v-model="fromData.name" placeholder="请输入行动名称"></el-input>
                    </el-form-item>
                    <el-form-item label="行动简介">
                        <el-input type="textarea" maxlength="200" show-word-limit :rows="3" v-model="fromData.introduction" placeholder="请输入行动简介"></el-input>
                    </el-form-item>

                </el-form>
            </div>
            <div>
                <el-form ref="form2" label-position="top" label-width="80px" :model="fromData" :rules="rules" @submit.native.prevent>
                    <el-form-item label="关联人员" prop="actionUsers">
                        <!-- <el-input v-model="nickName" placeholder="请输入人员名称搜索"  @keyup.enter.native="getListUser"></el-input> -->
                        <el-table ref="userTable" v-loading="userloading" 
                        :data="userList" 
                        @selection-change="handleSelectionChange" style="margin-top:10px;" height="calc(100vh - 330px)">
                            <el-table-column type="selection" width="50" align="center" />
                            <el-table-column label="人员名称" align="center" prop="nickName" />
                            <el-table-column label="人员角色" align="center" prop="roleNames" :show-overflow-tooltip="true" />
                            <el-table-column label="行动小组" align="center" prop="deptName" :show-overflow-tooltip="true" />
                        </el-table>
                    </el-form-item>
                </el-form>
            </div>
            <div>
                <el-form label-position="top" label-width="80px" :model="fromData">
                    <el-form-item label="行动位置">
                        <el-select
                            style="width: 100%;height: 40px;"
                            v-model="fromData.address"
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请输入关键词"
                            :remote-method="remoteMethod"
                            @change="addressSelect"
                            :loading="addressSearchLoading">
                            <el-option
                            v-for="item in addressOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.name">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div class="w-map" id="container2"></div>
            </div>
        </div>
    </div>
</template>

<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import { amapKey, amapSecretkey } from "@/utils/use-g-map"
import { listPlan } from "@/api/command/plan";
import { addAction, editAction, detAction, userSelectUse} from "@/api/command/moveabout";
export default {
    data() {
        return {
            fromData: {
                address: "",
                id: "",
                introduction: "",
                lat: "",
                lon: "",
                name: "",
                pilotId: "",
                pilotName: "",
                planId: "",
                planName: "",
                remark: "",
                status: 1,
                actionUsers: []
            },
            map:null,
            addressOptions: [],
            addressMarker: null,
            addressSearchLoading: false,
            placeSearch: null,
            userList: [],
            userloading: false,
            planList: [],
            geocoder: null,
            rules: {
                name: [
                    { required: true, message: "行动名称不能为空", trigger: "blur" }
                ],
                actionUsers: [
                    { required: true, message: "关联人员不能为空", trigger: "blur" }
                ]
            },
            nickName: ""
        }
    },
    created() {
        this.getListUser()
    },
    mounted() {
        this.initMap()
    },
    methods: {
        getDet(id) {
            let _this = this
            detAction(id).then(res => {
                console.log("行动详情",res)
                this.fromData = res.data
                if(_this.fromData.lat && _this.fromData.lon) {
                    const icon = new AMap.Icon({
                        size: new AMap.Size(24, 24),
                        image: require("@/assets/images/mapicon/a16.png"),
                        imageSize: new AMap.Size(24, 24),
                        imageOffset: new AMap.Pixel(0, 0),
                    });
                    this.addressMarker = new AMap.Marker({
                        position: [_this.fromData.lon, _this.fromData.lat],
                        icon: icon,
                        offset: new AMap.Pixel(-12, -12),
                    });
                    _this.map.add(this.addressMarker)
                    _this.map.setZoomAndCenter(15,[_this.fromData.lon, _this.fromData.lat])
                }
                let arr = _this.fromData.actionUsers
                let selectlist = _this.userList.filter(item => {
                    return arr.findIndex(obj => obj.userId == item.userId) != -1;
                })
                selectlist.forEach(item => {
                    this.$refs.userTable.toggleRowSelection(item);
                })
            })
        },
        async addSave(){
            let valid1 = await this.$refs["form1"].validate()
            let valid2 = await this.$refs["form2"].validate()
            // console.log("表单提交",this.fromData)
            if(valid1 && valid2) {
                if(this.fromData.id == "") {
                    addAction(this.fromData).then(res => {
                        // console.log("res",res)
                        this.$router.go(-1)
                    })
                } else {
                    editAction(this.fromData).then(res => {
                        // console.log("res",res)
                        this.$router.go(-1)
                    })
                }
            }
            
        },
        cancel(){
            this.$router.go(-1)
        },
       
        handleSelectionChange(selection){
            console.log("选中的人员",selection)
            this.fromData.actionUsers = selection
            // let arr1 = []
            // let arr2 = []
            // selection.forEach(item => {
            //     arr1.push(item.userId)
            //     arr2.push(item.nickName)
            // });
            // this.fromData.pilotId = arr1.join(',')
            // this.fromData.pilotName = arr2.join(',')
        },
        getListUser() {
            let params = {
                nickName: this.nickName
            }
            this.userloading = true
            userSelectUse(params).then(res => {
                console.log("人员列表",res)
                this.userList = res.data
                this.userloading = false
            })
        },
        initMap() {
            let _this = this;
            window._AMapSecurityConfig = {
                securityJsCode: amapSecretkey
            }
            AMapLoader.load({
                key: amapKey,
                version: "2.0",
                plugins: [
                    "AMap.PlaceSearch",
                    "AMap.Geocoder"
                ],
            })
            .then((AMap) => {
                _this.map = new AMap.Map("container2", {
                    zoom: 12,
                    mapStyle: "amap://styles/whitesmoke",
                });
                _this.placeSearch = new AMap.PlaceSearch({city: "全国"});
                _this.geocoder = new AMap.Geocoder({
                    radius: 1000 //范围，默认：500
                });
                _this.map.on("click", this.mapClick)
                if (_this.$route.params.id != 0) {
                    _this.getDet(this.$route.params.id);
                }
            })
            .catch((e) => {
                console.log(e);
            });
        },
        mapClick(e) {
            let _this = this
            if(this.addressMarker) {
                this.addressMarker.remove()
                this.addressMarker = null
            }
            const icon = new AMap.Icon({
                size: new AMap.Size(24, 24),
                image: require("@/assets/images/mapicon/a16.png"),
                imageSize: new AMap.Size(24, 24),
                imageOffset: new AMap.Pixel(0, 0),
            });
            this.addressMarker = new AMap.Marker({
                position: [e.lnglat.lng, e.lnglat.lat],
                icon: icon,
                offset: new AMap.Pixel(-12, -12),
            });
            this.map.add(this.addressMarker)
            _this.geocoder.getAddress([e.lnglat.lng, e.lnglat.lat],(status,result) => {
                if(status == "complete" && result.regeocode) {
                    _this.fromData.address = result.regeocode.formattedAddress
                    _this.fromData.lat = e.lnglat.lat
                    _this.fromData.lon = e.lnglat.lng
                }
            })
        },
        // 地址搜索
        remoteMethod(val){
            let _this = this
            if(val !== "" ) {
                this.addressSearchLoading = true
                this.placeSearch.search(val,(status,result) => {
                if(status == "complete" && result.poiList) {
                    _this.addressSearchLoading = false
                    _this.addressOptions = result.poiList.pois
                }
                })

            } else {
                this.addressOptions = []
            }
        },
        addressSelect(val){
            // console.log(val)
            let index = this.addressOptions.findIndex(item => item.name == val)
            this.map.setZoomAndCenter(15,[this.addressOptions[index].location.lng,this.addressOptions[index].location.lat])
            if(this.addressMarker) {
                this.addressMarker.remove()
                this.addressMarker = null
            }
            const icon = new AMap.Icon({
                size: new AMap.Size(24, 24),
                image: require("@/assets/images/mapicon/a16.png"),
                imageSize: new AMap.Size(24, 24),
                imageOffset: new AMap.Pixel(0, 0),
            });
            this.addressMarker = new AMap.Marker({
                position: [this.addressOptions[index].location.lng,this.addressOptions[index].location.lat],
                icon: icon,
                offset: new AMap.Pixel(-12, -12),
            });
            this.map.add(this.addressMarker)
            this.fromData.address = this.addressOptions[index].name
            this.fromData.lat = this.addressOptions[index].location.lat
            this.fromData.lon = this.addressOptions[index].location.lng
        },
    }
}
</script>
<style lang="scss" scoped>
.app-container {
    background: #f5f5f5;
}
.w-title {
    width: 100%;
    height: 60px;
    line-height: 60px;
    font-size: 18px;
    color: #000;
    background: #fff;
    padding: 0 20px;
    box-shadow: 0 5px 15px #0000000d;
    border-radius: 5px 5px 0 0;
    position: relative;
    display: flex;
    justify-content: space-between;
}

.w-box {
    width: 100%;
    height: calc(100% - 60px);
    background: #fff;
    border-radius:0 0 5px 5px;
    display: flex;
    &>div {
        width: 33.3%;
        height: 100%;
        overflow: auto;
        padding: 20px;
        .w-map {
            width: 100%;
            height: 300px;
        }
        .w-header {
            width: 100%;
            font-size: 18px;
            height: 50px;
            line-height: 50px;
            color: #000;
        }
    }
}
</style>