import request from '@/utils/request'

// 获取正在执行任务列表
export function curreyTask(query) {
    return request({
      url: '/task/record/listInProgress',
      method: 'get',
      params: query
    })
}

// 获取任务记录列表
export function recordTask(query) {
  return request({
    url: '/task/record/list',
    method: 'get',
    params: query
  })
}

// 获取任务记录详情
export function recordTaskDet(id) {
  return request({
    url: '/task/record/' + id,
    method: 'get',
  })
}

// 获取任务记录详情轨迹
export function recordTaskOsdDet(params) {
  return request({
    url: '/task/record/osd/list',
    method: 'get',
    params
  })
}

// 获取任务记录详情媒体信息
export function recordTaskMediaDet(params) {
  return request({
    url: '/task/record/media/list',
    method: 'get',
    params
  })
}