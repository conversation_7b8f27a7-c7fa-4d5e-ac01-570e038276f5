<template>
    <div :class="className" :style="{height:height,width:width}">
        <div class="w-height">
            <h3>未来7天任务</h3>
            <img @click="tiao('/cruise/task')" src="@/assets/images/index/godtl.png" >
        </div>
        <div class="w-box">
            <div ref="droneType" style="width:100%;height:100%;"></div>
        </div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts';
  import { indexTaskFutureStatistics } from '@/api/index/index'
  import resize from '../mixins/resize'
  export default {
    mixins: [resize],
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '350px'
      }
    },
    data() {
      return {
        x: [],
        y: [],
        loading: false,
        chart: null,

      }
    },
    mounted() {
        this.getData()
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        tiao(url){
            this.$router.push(url)
        },
        getData(){
            let params = {}
            this.loading = true
            indexTaskFutureStatistics(params).then(res => {
                console.log("未来7天循环任务统计",res)
                this.tableData = res.data
                this.tableData.forEach(item => {
                    this.x.push(item.date)
                    this.y.push(item.count)
                })
                this.$nextTick(() => {
                    this.initChart()
                })
                this.loading = false
            }).catch(e => {
                this.loading = false
            })
        },
        initChart() {
            this.chart = echarts.init(this.$refs.droneType)
            this.chart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                    }
                },
                grid: {
                    top: 10,
                    left: '2%',
                    right: '2%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: this.x,
                    axisTick: {
                        alignWithLabel: true
                    }
                }],
                yAxis: [{
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    minInterval: 1
                }],
                series: [
                    {
                        name: '数量',
                        type: 'bar',
                        barWidth: '60%',
                        data: this.y
                    }
                ]
            })
        }
    }
  }
  </script>

  <style lang="scss" scoped>
    .w-height {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(270deg, #BFD3F7 0%, #6694f0 100%);
        border-radius: 6px 6px 0px 0px;
        color: #FFFFFF;
        padding: 0 15px;
        h3 {
            font-size: 16px;
            font-weight: 700;
        }
        img {
            width: 25px;
        }
    }
    .w-box {
        width: 100%;
        height: calc(100% - 40px);
        padding: 10px 15px;
    }
  </style>
  